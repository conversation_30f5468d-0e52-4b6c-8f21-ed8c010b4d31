import React, { useState, useEffect } from "react";
import styles from "./ProfileContent.module.scss";
import { buttonEffect } from "@/animations/animations";
import { motion } from "framer-motion";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import { useSnapshot } from "valtio";
import { useAccount, useNetwork, useDisconnect } from "wagmi";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/ReactToastify.min.css";
import { BaseError } from "viem";
import BlockchainInfo from "../BlockchainInfo/BlockchainInfo";
import UserProfile from "./UserProfile/UserProfile";
import PurchaseHistory from "./PurchaseHistory/PurchaseHistory";
import PointSystem from "@/components/MobilePage/LeaderBoard/PointSystem"; // 改用移动端的组件
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";

function ProfileContent() {
  const [showPointSystem, setShowPointSystem] = useState(false);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const { isConnected } = useAccount();
  const { chain } = useNetwork();

  const showMobileError = (message: string) => {
    console.log("Showing error:", message); // 调试日志
    toast.error(message); // 使用 react-toastify
    // 备用方案
    // if (typeof window !== 'undefined') {
    //   alert(message);
    // }
  };

  useEffect(() => {
    // if (!isConnected) {
    //   showMobileError("Please Connect Wallet");
    //   return;
    // }
    if (chain?.unsupported) {
      showMobileError("Please Switch Network");
      return;
    }
  }, [chain]);

  const { disconnect } = useDisconnect({
    onError: (error) => {
      showMobileError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      toast.success("Wallet disconnected");
    },
  });

  if (showPointSystem) {
    return (
      <div className={styles.pointSystemWrapper}>
        <div
          className={styles.closeButton}
          onClick={() => setShowPointSystem(false)}
        >
          <img src={crossIcon.src} alt="close" />
        </div>
        <PointSystem />
      </div>
    );
  }

  return (
    <>
      <div className={styles.headerWrapper}>
        <div className={styles.header}>
          <h1 className={styles.title}>Profile</h1>
          <h2 className={styles.subTitle}>DETAILS OF PROFILE</h2>
        </div>
      </div>
      <div className={styles.scrollArea}>
        {isConnected ? (
          <div className={styles.profileContent}>
            <UserProfile />
            <BlockchainInfo />
            <PurchaseHistory />
            <motion.button
              onClick={() => setShowPointSystem(true)}
              className={styles.actionButton}
              whileTap={buttonEffect.tap}
            >
              View Point System
            </motion.button>
            <motion.button
              onClick={() => disconnect()}
              className={styles.loginButton}
              whileTap={buttonEffect.tap}
            >
              Logout
            </motion.button>
          </div>
        ) : (
          <div className={styles.loginPrompt}>
            <p>Please connect your wallet to launch</p>
            <br />
            <motion.div
              className={styles.loginButton}
              whileTap={buttonEffect.tap}
              onClick={() => {
                connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
              }}
            >
              Log in <span>&nbsp;or&nbsp;</span> Register Now
            </motion.div>
          </div>
        )}
      </div>
    </>
  );
}

export default ProfileContent;
