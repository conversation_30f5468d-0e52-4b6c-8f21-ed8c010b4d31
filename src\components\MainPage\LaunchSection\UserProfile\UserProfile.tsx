import styles from "./UserProfile.module.scss";
import locationIcon from "@/assets/icons/plusModal/locationIcon.png";
import ethIcon from "@/assets/icons/plusModal/ethIcon.png";
import { useAccount, useNetwork } from "wagmi";
import Link from "next/link";
import { getRandomAvatar } from "@/constants/avatarsList";

const UserProfile = () => {
  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();

  const trimWalletAddress = (address: string) =>
    address.slice(0, 6) + "..." + address.slice(-4);

  const SpacerY = () => <div className={styles.spacingY} />;

  return (
    <>
      {isConnected && !chain?.unsupported && address ? (
        <div className={styles.userProfile}>
          <div className={styles.titleWrapper}>
            <h1>user profile</h1>
            <h2>area bought</h2>
          </div>
          <SpacerY />
          <div className={styles.avatar}>
            {/* <h1 className={styles.letter}>C</h1> */}
            <img
              src={getRandomAvatar().image}
              alt="avatar"
              width={"100%"}
              height={"100%"}
            />
          </div>
          <SpacerY />
          <div className={styles.infoWrapper}>
            <h1>administer</h1>
            <div className={styles.subtitle}>
              <img
                src={locationIcon.src}
                alt="location icon"
                width={16}
                height={16}
              />
              <h2>null</h2>
            </div>
            <div className={styles.subtitle}>
              <img src={ethIcon.src} alt="eth icon" width={14} />
              <h2>{trimWalletAddress(address.toString())}</h2>
            </div>
            {/* <Link
              href="/statistics"
              replace
              style={{ color: "yellow", fontSize: "1.2rem" }}
            >
              Point Systems
            </Link> */}
          </div>
        </div>
      ) : null}
    </>
  );
};

export default UserProfile;
