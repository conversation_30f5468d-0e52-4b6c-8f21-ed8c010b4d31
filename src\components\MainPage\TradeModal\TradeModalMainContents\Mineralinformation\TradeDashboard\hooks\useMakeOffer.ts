import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useLayoutEffect, useState } from "react";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { BaseError, parseEther } from "viem";
import { showError, showSuccess } from "@/lib/notification";
import { minesDetails } from "@/constants/mineDetails";
import { useMediaQuery } from "usehooks-ts";
import { extractErrorType } from "@/utils/errorHandling";

const useMakeOffer = (onClose: () => void) => {
  // const isMobile = useMediaQuery("(max-width: 1279px)");

  // // Notification function that works for both mobile and desktop
  // const notify = (message: string, type: 'error' | 'success') => {
  //   if (isMobile) {
  //     if (type === 'error') {
  //       toast.error(message);
  //     } else {
  //       toast.success(message);
  //     }
  //   } else {
  //     if (type === 'error') {
  //       showError(message);
  //     } else {
  //       showSuccess(message);
  //     }
  //   }
  // };
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [avaliableNfts, setAvaliableNfts] = useState(0n);
  const [numberOfItem, setNumberOfItem] = useState(0);
  const [pricePerToken, setPricePerToken] = useState(0.01);
  const [totalEth, setTotalEth] = useState(numberOfItem * pricePerToken);
  const [expirationDateUnixTime, setExpirationDateUnixTime] = useState(0);
  const [isMakingOffer, setIsMakingOffer] = useState(false);
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const MAX_OFFER_AMOUNT = 200;

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setAsteroidAddress(networkConfigs[chain.id].asteroidAddress);
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setAsteroidAddress("0x");
      setMarketplaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  useEffect(() => {
    // if (numberOfItem > avaliableNfts) {
    //   setNumberOfItem(0);
    // }
    setTotalEth(numberOfItem * pricePerToken);
  }, [numberOfItem, avaliableNfts, pricePerToken]);

  useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "balanceOf",
    args: [address ?? "0x", BigInt(asteroidMineNumber)],
    enabled: isConnected && asteroidAddress !== "0x" && address !== "0x",
    watch: true,
    onSuccess: (data) => {
      setAvaliableNfts(data);
    },
  });

  const { config: makeOfferConfig, error: prepareError } =
    usePrepareContractWrite({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "makeOffer",
      args: [
        BigInt(asteroidMineNumber),
        BigInt(numberOfItem),
        parseEther(pricePerToken.toString()),
        BigInt(expirationDateUnixTime),
      ],
      value: parseEther(totalEth.toString()),
      enabled:
        isConnected &&
        marketplaceAddress !== "0x" &&
        asteroidMineNumber !== 0 &&
        numberOfItem !== 0 &&
        expirationDateUnixTime !== 0 &&
        pricePerToken !== 0,
    });

  const { write: makeOfferWrite, data: makeOfferData } = useContractWrite({
    ...makeOfferConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsMakingOffer(false);
    },
  });

  const { isLoading: isWaitingForMakeOffer } = useWaitForTransaction({
    confirmations: 5,
    hash: makeOfferData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Made Offer!");
      setIsMakingOffer(false);
      onClose();
    },
    onError: (error) => {
      showSuccess("Successfully Made Offer!");
      setIsMakingOffer(false);
    },
  });

  const makeOffer = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (!makeOfferWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      } else {
        showError(
          "Unable to process offer creation. Please verify contract connection or switch to a supported network.",
        );
      }
      return;
    }

    setIsMakingOffer(true);
    try {
      makeOfferWrite?.();
    } catch (error) {
      setIsMakingOffer(false);
      showError(extractErrorType(error) || "Failed to make offer");
    }
  };

  return {
    avaliableNfts,
    numberOfItem,
    setNumberOfItem,
    pricePerToken,
    setPricePerToken,
    totalEth,
    setExpirationDateUnixTime,
    isMakingOffer,
    makeOffer,
    isWaitingForMakeOffer,
    MAX_OFFER_AMOUNT,
  };
};

export default useMakeOffer;
