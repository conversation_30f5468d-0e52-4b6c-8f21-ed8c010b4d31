import styles from "./InformationCard.module.scss";
import locationIcon from "@/assets/icons/plusModal/locationIcon.png";
import ethIcon from "@/assets/icons/plusModal/ethIcon.png";
import { buyerInformation } from "@/sampleAssets/constants/buyerInformation";
import { useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";

type Unpack<T> = T extends (infer U)[] ? U : T;

interface InformationCardProps {
  information: Unpack<typeof buyerInformation>;
}
const InformationCard = ({ information }: InformationCardProps) => {
  const trimWalletAddress = (address: string) =>
    address.slice(0, 6) + "..." + address.slice(-4);
  const { chain } = useNetwork();

  return (
    <div
      className={styles.container}
      onClick={() =>
        window.open(
          networkConfigs[chain?.id as keyof typeof networkConfigs]
            .etherscanAddress + information.walletAddress,
        )
      }
    >
      <div className={styles.contentWrapper}>
        <div className={styles.avatar}>
          <h1 className={styles.letter}>{information.name.charAt(0)}</h1>
        </div>
        <div className={styles.infoWrapper}>
          <h1>{information.name}</h1>
          <div className={styles.subtitle}>
            <img
              src={locationIcon.src}
              alt="location icon"
              width={10}
              height={10}
            />
            <h2>null</h2>
          </div>
          <div className={styles.subtitle}>
            <img src={ethIcon.src} alt="eth icon" />
            <h2>{trimWalletAddress(information.walletAddress)}</h2>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InformationCard;
