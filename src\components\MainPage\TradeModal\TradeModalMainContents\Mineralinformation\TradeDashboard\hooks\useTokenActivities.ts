import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { mineCardStore } from "@/stores/mineCard";
import { useSnapshot } from "valtio";
import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";

const useTokenActivities = () => {
  const activityTypes = [
    "LISTING",
    "SALE",
    "TRANSFER",
    "CANCEL LISTING",
    "MAKE OFFER",
    "CANCEL OFFER",
    "ACCEPT OFFER",
  ];
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [tokenActivitiesFilteredByType, setTokenActivitiesFilteredByType] =
    useState<typeof tokenActivities>([]);
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const { data: tokenActivities } = useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getTokenActivities",
    args: [BigInt(asteroidMineNumber)],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
  });

  const { data: marketplaceVaultAddress } = useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "marketplaceVaultContract",
    enabled: isConnected && marketpaceAddress !== "0x",
    watch: true,
  });

  useEffect(() => {
    // filter the tokenActivities by transfer type
    if (tokenActivities && tokenActivities?.length !== 0) {
      // Sort by timestamp in descending order (most recent first)
      const sortedActivities = [...tokenActivities].sort(
        (a, b) => Number(b.timestamp) - Number(a.timestamp),
      );
      setTokenActivitiesFilteredByType(sortedActivities);
    } else {
      setTokenActivitiesFilteredByType([]);
    }
  }, [tokenActivities]);

  return {
    tokenActivitiesFilteredByType,
    activityTypes,
    marketplaceVaultAddress,
  };
};

export default useTokenActivities;
