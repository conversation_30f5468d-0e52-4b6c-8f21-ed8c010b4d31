import { useEffect, useLayoutEffect, useState } from "react";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { networkConfigs } from "@/constants/networkConfigs";
import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError } from "viem";
import { minesDetails } from "@/constants/mineDetails";
import { extractErrorType } from "@/utils/errorHandling";

const useCancelListing = (onClose?: () => void) => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [allActiveListingSortedBySeller, setAllActiveListingSortedBySeller] =
    useState<typeof allActiveListingsForToken>([]);
  const [selectedListingId, setSelectedListingId] = useState(0n);
  const [isCancelingListing, setIsCancelingListing] = useState(false);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const formatUnixTimestamp = (timestamp: number): string => {
    if (!timestamp) return "";

    const date = new Date(timestamp * 1000);
    return date.toLocaleString("en-AU", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketplaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const { data: allActiveListingsForToken } = useContractRead({
    address: marketplaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getAllActiveListingsForToken",
    args: [BigInt(asteroidMineNumber)],
    enabled:
      isConnected && marketplaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
  });

  useEffect(() => {
    if (allActiveListingsForToken && address) {
      // add filter for time if expiration time is less than current time
      const currentTime = Math.floor(Date.now() / 1000);
      const filteredListings = allActiveListingsForToken
        .filter((listing) => Number(listing.expirationTime) > currentTime)
        .filter(
          (listing) => listing.seller.toLowerCase() === address?.toLowerCase(),
        );
      const sortedListings = filteredListings.sort((a, b) =>
        Number(a.pricePerToken - b.pricePerToken),
      );
      setAllActiveListingSortedBySeller(sortedListings);
    }
  }, [allActiveListingsForToken, address]);

  const { config: cancelListingConfig, error: prepareError } =
    usePrepareContractWrite({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "cancelListing",
      args: [selectedListingId],
      enabled:
        isConnected && marketplaceAddress !== "0x" && selectedListingId !== 0n,
    });

  const { write: cancelListingWrite, data: cancelListingData } =
    useContractWrite({
      ...cancelListingConfig,
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
        setIsCancelingListing(false);
      },
    });

  const { isLoading: isWaitingForCancelListing } = useWaitForTransaction({
    confirmations: 5,
    hash: cancelListingData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Cancelled!");
      setIsCancelingListing(false);
      onClose?.();
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsCancelingListing(false);
    },
  });

  const cancelListing = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    // if (errorMessage) {
    //   showError(errorMessage);
    //   return;
    // }

    if (!cancelListingWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      } else {
        showError(
          "Unable to process listing cancellation. Please verify contract connection or switch to a supported network.",
        );
      }
      return;
    }

    setIsCancelingListing(true);
    try {
      cancelListingWrite?.();
    } catch (error) {
      setIsCancelingListing(false);
      showError(extractErrorType(error) || "Failed to cancel listing");
    }
  };

  return {
    allActiveListingSortedBySeller,
    formatUnixTimestamp,
    setSelectedListingId,
    selectedListingId,
    cancelListing,
    isWaitingForCancelListing,
    isCancelingListing,
  };
};

export default useCancelListing;
