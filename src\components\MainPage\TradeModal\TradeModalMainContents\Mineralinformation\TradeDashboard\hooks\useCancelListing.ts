import { useEffect, useLayoutEffect, useState } from "react";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { networkConfigs } from "@/constants/networkConfigs";
import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError } from "viem";
import { minesDetails } from "@/constants/mineDetails";
import { extractErrorType } from "@/utils/errorHandling";

type Listing = {
  listingId: bigint;
  seller: `0x${string}`;
  tokenId: bigint;
  amount: bigint;
  pricePerToken: bigint;
  expirationTime: bigint;
  active: boolean;
  timestamp: bigint;
  paymentToken: `0x${string}`;
};

// Define the structure of the paginated response
type PaginatedListingsResponse = [Listing[], bigint];

const useCancelListing = (onClose?: () => void) => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [userListingsForToken, setUserListingsForToken] = useState<Listing[]>(
    [],
  );
  const [selectedListingId, setSelectedListingId] = useState(0n);
  const [isCancelingListing, setIsCancelingListing] = useState(false);
  const [limit, setLimit] = useState(50n);
  const [totalUserListingsForToken, setTotalUserListingsForToken] =
    useState(0n);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const formatUnixTimestamp = (timestamp: number): string => {
    if (!timestamp) return "";
    const date = new Date(timestamp * 1000);
    return date.toLocaleString("en-AU", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketplaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine, getSelectedMineId]);

  const { data: userTokenListingsData, refetch: refetchUserTokenListings } =
    useContractRead({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "getActiveListingsBySellerAndTokenPaginated",
      args: [address ?? "0x", BigInt(asteroidMineNumber), 0n, limit],
      enabled:
        isConnected &&
        marketplaceAddress !== "0x" &&
        address !== undefined &&
        asteroidMineNumber !== 0,
      watch: true,
      onSuccess: (data: PaginatedListingsResponse) => {
        if (data) {
          // Data is already filtered by seller and tokenId, and for active/non-expired status by the contract.
          // Sort if needed, e.g., by price or timestamp
          const sortedListings = [...data[0]].sort((a: Listing, b: Listing) =>
            Number(a.pricePerToken - b.pricePerToken),
          );
          setUserListingsForToken(sortedListings);
          setTotalUserListingsForToken(data[1]);
        } else {
          setUserListingsForToken([]);
          setTotalUserListingsForToken(0n);
        }
      },
    }) as { data?: PaginatedListingsResponse; refetch: () => void };

  const { config: cancelListingConfig, error: prepareError } =
    usePrepareContractWrite({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "cancelListing",
      args: [selectedListingId],
      enabled:
        isConnected && marketplaceAddress !== "0x" && selectedListingId !== 0n,
    });

  const { write: cancelListingWrite, data: cancelListingData } =
    useContractWrite({
      ...cancelListingConfig,
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
        setIsCancelingListing(false);
      },
    });

  const { isLoading: isWaitingForCancelListing } = useWaitForTransaction({
    confirmations: 5,
    hash: cancelListingData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Cancelled!");
      setIsCancelingListing(false);
      refetchUserTokenListings();
      onClose?.();
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsCancelingListing(false);
    },
  });

  const cancelListing = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (!cancelListingWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      } else {
        showError(
          "Unable to process listing cancellation. Please verify contract connection or switch to a supported network.",
        );
      }
      return;
    }

    setIsCancelingListing(true);
    try {
      cancelListingWrite?.();
    } catch (error) {
      setIsCancelingListing(false);
      showError(extractErrorType(error) || "Failed to cancel listing");
    }
  };

  return {
    allActiveListingSortedBySeller: userListingsForToken,
    formatUnixTimestamp,
    setSelectedListingId,
    selectedListingId,
    cancelListing,
    isWaitingForCancelListing,
    isCancelingListing,
    totalUserListings: totalUserListingsForToken,
    setLimit,
  };
};

export default useCancelListing;
