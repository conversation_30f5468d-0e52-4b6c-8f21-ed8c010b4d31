/**
 * Extract the actual error message from a contract error
 * @param error The contract error object
 * @returns The extracted error message
 */
export const extractErrorReason = (error: any) => {
  // Split error message into lines
  const lines = error.message.split("\n");

  // Find the line containing the actual error message (e.g., "Already purchased")
  for (const line of lines) {
    const trimmedLine = line.trim();
    // If the line starts with a parenthesis and doesn't contain "string reason"
    if (trimmedLine.startsWith("(") && !trimmedLine.includes("string reason")) {
      // Remove parentheses and return the actual error message
      return trimmedLine.slice(1, -1);
    }
  }

  // If no matching error message is found, return shortMessage or original message
  return error.shortMessage || error.message;
};
