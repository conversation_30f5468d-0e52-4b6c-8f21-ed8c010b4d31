import { plusModalStore } from "@/stores/plusModal";
import Buyer from "./Buyer/Buyer";
import styles from "./MineBody.module.scss";
import snakeIcon from "@/assets/icons/minesNavigation/snakeIcon.png";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
import { mineCardStore } from "@/stores/mineCard";
import useSpecialEvent from "./hooks/useSpecialEvent";
import { useAccount, useNetwork } from "wagmi";
import { formatEther } from "viem";
import Rewards from "@/components/MobilePage/LeaderBoard/Rewards/Rewards";
import peckShieldIcon from "@/assets/icons/peckShieldIcon.png";
import useRedpacket from "@/components/MainPage/RedPocket/hooks/useRedpacket";

interface InformationProps {
  title: string;
  subtitle: string;
  borderBottom?: boolean;
  showLogo?: boolean;
}
interface ComponentProps {
  setSelectedComponent: (component: string) => void;
}

const MineBody: React.FC<ComponentProps> = ({ setSelectedComponent }) => {
  const { isConnected } = useAccount();
  const { chain } = useNetwork();
  const Information = ({
    title,
    subtitle,
    borderBottom,
    showLogo,
  }: InformationProps) => (
    <>
      <div
        className={styles.information}
        style={{ borderBottom: borderBottom ? undefined : "none" }}
      >
        <div className={styles.logoWrapper}>
          <div>
            <h1 className={styles.title}>{title}</h1>
            <h1 className={styles.subtitle}>{subtitle}</h1>
          </div>
          {showLogo ? (
            <img src={snakeIcon.src} alt={"snake icon"} height={35} />
          ) : null}
        </div>
      </div>
    </>
  );
  const formatTimestamp = (timestamp: bigint | undefined): string => {
    if (!timestamp) return "N/A";
    const date = new Date(Number(timestamp) * 1000);
    return date
      .toLocaleString("en-AU", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      })
      .replace(",", "");
  };
  // const {
  //   totalParticipants,
  //   totalReward,
  //   participateTime,
  //   claimableReward,
  //   purchaseTime,
  // } = useSpecialEvent();
  const { totalParticipants, avaliableNftBalance, startTime, endTime } =
    useRedpacket();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  return (
    <div className={styles.container}>
      <div className={styles.titleInformation}>
        <div className={styles.imageFrame}>
          <img src={information.mineImages[0]} alt={information.name} />
        </div>
        <div>
          <Information title={"price"} subtitle={`$${information.minePrice}`} />
          <Information
            title={"Reserve estimate"}
            subtitle={information.mineStorage}
          />
        </div>
      </div>
      <div className={styles.information}>
        <div className={styles.logoWrapper}>
          <div>
            <h1 className={styles.title}>How to Participate</h1>
            <h1 className={styles.subtitle}>- Press the Claim Now button</h1>
            {/* <h1 className={styles.subtitle}>- Spend 1 HSK to Join the Event</h1>
            <h1 className={styles.subtitle}>- 200 HSK Initial Reward Pool</h1>
            <h1 className={styles.subtitle}>
              - HSK spent will be 100% returned as part of your Reward
            </h1>
            <h1 className={styles.subtitle}>
              - Reward Formula: 1 HSK + (200 HSK ÷ Total Participants)
            </h1> */}
          </div>
        </div>
      </div>
      <Information
        title={"Total Participant"}
        subtitle={totalParticipants.toString()}
        borderBottom
      />
      <Information
        title={"Avaliable Claimable NFTs"}
        subtitle={`${avaliableNftBalance.toString()}`}
        borderBottom
      />
      <Information
        title={"Event Start Time"}
        subtitle={formatTimestamp(startTime)}
        borderBottom
      />
      <Information
        title={"Event End Time"}
        subtitle={formatTimestamp(endTime)}
        borderBottom
      />
      {/* <Information
        title={"Claimable Reward"}
        subtitle={`1 HSK + ${Number(formatEther(claimableReward)).toFixed(
          2,
        )} HSK`}
        borderBottom
      /> */}
      <Buyer setSelectedComponent={setSelectedComponent} />
      {/* <Rewards /> */}
      <div className={styles.auditArea}>
        <h5 className={styles.auditText}>
          *Audited by
          <img
            className={styles.auditImage}
            src={peckShieldIcon.src}
            alt="peckShieldIcon"
            height={14}
          />
        </h5>
      </div>
    </div>
  );
};

export default MineBody;
