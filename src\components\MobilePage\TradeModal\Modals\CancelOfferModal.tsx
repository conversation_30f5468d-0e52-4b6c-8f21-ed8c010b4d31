import { FC } from "react";
import styles from "./CancelOfferModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { formatEther } from "viem";
import useCancelOffer from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useCancelOffer";

interface CancelOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CancelOfferModal: FC<CancelOfferModalProps> = ({ isOpen, onClose }) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const {
    allActiveOfferSortedByPrice,
    formatUnixTimestamp,
    cancelOffer,
    isCancelingOffer,
    isWaitingForCancelOffer,
    setSelectedOfferId,
    selectedOfferId,
  } = useCancelOffer(onClose);

  const handleMouseOver = (index: number) => {
    if (allActiveOfferSortedByPrice) {
      setSelectedOfferId(allActiveOfferSortedByPrice[index].offerId);
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.wrapper}>
      <div className={styles.headerWrapper}>
        <h3 className={styles.header}>CANCEL OFFER</h3>
        <div className={styles.closeButton} onClick={onClose}>
          <img src={crossIcon.src} alt="cross icon" />
        </div>
      </div>

      <div className={styles.contentWrapper}>
        <div className={styles.scrollArea}>
          <div className={styles.contentFrame}>
            <div className={styles.derlordInfo}>
              <h3>{selectedMine.name}</h3>
            </div>
            <div className={styles.listingsTable}>
              <div className={styles.tableHeader}>
                <div>Price per Token</div>
                <div>Quantity</div>
                <div>Expiration</div>
                <div>Action</div>
              </div>
              <div className={styles.tableBodyContainer}>
                <div className={styles.tableBody}>
                  {allActiveOfferSortedByPrice &&
                  allActiveOfferSortedByPrice.length > 0 ? (
                    allActiveOfferSortedByPrice.map((listing, index) => (
                      <div className={styles.tableRow} key={index}>
                        <div>{formatEther(listing.pricePerToken)} {selectedMine.currency}</div>
                        <div>{listing.amount.toString()}</div>
                        <div>
                          {formatUnixTimestamp(Number(listing.expirationTime))}
                        </div>
                        <div>
                          <button
                            className={styles.cancelButton}
                            onMouseEnter={() => handleMouseOver(index)}
                            onClick={cancelOffer}
                            disabled={
                              isCancelingOffer || isWaitingForCancelOffer
                            }
                          >
                            {(isCancelingOffer || isWaitingForCancelOffer) &&
                            selectedOfferId === listing.offerId
                              ? "Processing..."
                              : "CANCEL"}
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div style={{ paddingLeft: "1rem" }}>
                      No active offers available
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CancelOfferModal;
