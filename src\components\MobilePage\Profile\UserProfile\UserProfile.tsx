import styles from "./UserProfile.module.scss";
import locationIcon from "@/assets/icons/plusModal/locationIcon.png";
import ethIcon from "@/assets/icons/plusModal/ethIcon.png";
import { useAccount, useNetwork } from "wagmi";
import Image from "next/image";
import { getRandomAvatar } from "@/constants/avatarsList";

const UserProfile = () => {
  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();

  const trimWalletAddress = (address: string) =>
    address.slice(0, 6) + "..." + address.slice(-4);

  const SpacerY = () => <div className={styles.spacingY} />;

  return (
    <>
      {isConnected && !chain?.unsupported && address ? (
        <div className={styles.userProfile}>
          <SpacerY />
          <div className={styles.avatarWrapper}>
            <div className={styles.avatar}>
              <img
                src={getRandomAvatar().image}
                alt="avatar"
                width={"100%"}
                height={"100%"}
              />
            </div>
          </div>
          <SpacerY />
          <div className={styles.infoWrapper}>
            <h1>administer</h1>
            <div className={styles.subtitle}>
              <Image
                src={locationIcon.src}
                alt="location icon"
                width={16}
                height={16}
              />
              <h2>null</h2>
            </div>
            <div className={styles.subtitle}>
              <Image src={ethIcon.src} alt="eth icon" width={14} height={14} />
              <h2>{trimWalletAddress(address.toString())}</h2>
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
};

export default UserProfile;
