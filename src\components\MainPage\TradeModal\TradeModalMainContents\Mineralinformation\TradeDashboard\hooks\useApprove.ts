import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError } from "viem";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
// remove array type
type Unpacked<T> = T extends (infer U)[] ? U : T;

const useApprove = () => {
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [isApprovedForAll, setIsApprovedForAll] = useState(false);
  const [isApproving, setIsApproving] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { chain, chains } = useNetwork();
  const { isConnected, address } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setAsteroidAddress(networkConfigs[chain.id].asteroidAddress);
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setAsteroidAddress("0x");
      setMarketplaceAddress("0x");
    }
  }, [chain]);

  useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "isApprovedForAll",
    args: [address ?? "0x", marketplaceAddress],
    enabled:
      isConnected &&
      asteroidAddress !== "0x" &&
      marketplaceAddress !== "0x" &&
      address !== undefined,
    watch: true,
    onSuccess: (data) => {
      setIsApprovedForAll(data);
    },
    onError: () => {
      setIsApprovedForAll(false);
    },
  });

  const { config: approveConfig } = usePrepareContractWrite({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "setApprovalForAll",
    args: [marketplaceAddress, true],
    enabled:
      isConnected && asteroidAddress !== "0x" && marketplaceAddress !== "0x",
    onError: (error: any) => {
      if (error.cause?.reason) {
        setErrorMessage(error.cause.reason);
      } else {
        setErrorMessage(error.shortMessage || error.message);
      }
    },
  });

  const { write: approveWrite, data: approveData } = useContractWrite({
    ...approveConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsApproving(false);
    },
  });

  const { isLoading: isWaitingForApprove } = useWaitForTransaction({
    confirmations: 5,
    hash: approveData?.hash,
    onSuccess: () => {
      showSuccess("Successfully approved all NFTs");
      setIsApproving(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsApproving(false);
    },
  });

  const handleApprove = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (errorMessage) {
      showError(errorMessage);
      return;
    }

    if (!approveWrite) {
      showError("Failed! Try different network");
      return;
    }

    setIsApproving(true);
    try {
      approveWrite?.();
    } catch (error) {
      setIsApproving(false);
      showError("Failed to approve NFTs");
    }
  };

  return { isApprovedForAll, handleApprove, isApproving, isWaitingForApprove };
};

export default useApprove;
