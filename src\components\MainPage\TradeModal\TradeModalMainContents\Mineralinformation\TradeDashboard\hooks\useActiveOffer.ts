import { useEffect, useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { networkConfigs } from "@/constants/networkConfigs";
import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";

type TOffer = {
  offerId: bigint;
  buyer: `0x${string}`;
  tokenId: bigint;
  amount: bigint;
  pricePerToken: bigint;
  expirationTime: bigint;
  active: boolean;
};
const useActiveOffer = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [allActiveOfferSortedByPrice, setAllActiveOfferSortedByPrice] =
    useState<TOffer[]>([]);
  const [limit, setLimit] = useState(50n);
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  // Function to convert Unix timestamp to human readable format
  const formatUnixTimestamp = (timestamp: number): string => {
    if (!timestamp) return "";

    const date = new Date(timestamp * 1000);
    return date.toLocaleString("en-AU", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const { data: allActiveOffer } = useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getActiveOffersPaginated",
    args: [BigInt(asteroidMineNumber), 0n, limit],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
    onSuccess: (data) => {
      setLimit(data[1]);
    },
  });

  useEffect(() => {
    if (allActiveOffer && address) {
      // add filter for time if expiration time is less than current time
      const currentTime = Math.floor(Date.now() / 1000);
      const filteredListings = allActiveOffer[0]
        .filter((offer) => Number(offer.expirationTime) > currentTime)
        .filter(
          (offer) => offer.buyer.toLowerCase() !== address?.toLowerCase(),
        );
      const sortedListings = filteredListings.sort((a, b) =>
        Number(b.pricePerToken - a.pricePerToken),
      );
      setAllActiveOfferSortedByPrice(sortedListings);
    }
  }, [allActiveOffer, address]);

  return { allActiveOfferSortedByPrice, formatUnixTimestamp };
};

export default useActiveOffer;
