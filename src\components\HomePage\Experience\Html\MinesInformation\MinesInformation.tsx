import { useInView } from "react-intersection-observer";
import styles from "./MinesInformation.module.scss";
import { minesDetails } from "@/constants/mineDetails";
import Image from "next/image";
import TeamsInformation from "../TeamsInformation/TeamsInformation";
import ProjectRoadmap from "../ProjectRoadmap/ProjectRoadmap";
import Explore from "../Explore/Explore";
import { useSnapshot } from "valtio";
import { universeAnimationStore } from "@/stores/universeAnimation";
import { MutableRefObject, useEffect, useRef } from "react";
import { CameraControls } from "@react-three/drei";
import { useRouter } from "next/navigation";
import { useElementSize, useMediaQuery } from "usehooks-ts";
import { scrollContainerStore } from "@/stores/scrollContainer";
import { useContainerDimensions } from "@/hooks/useContainerDimension";
import bulletinIcon from "@/assets/icons/minesNavigation/mineHeaderIcon.png";

type MinesInformationProps = {
  minesInformationRef: ReturnType<typeof useInView>["ref"];
  cameraControlsRef: MutableRefObject<CameraControls | undefined>;
  replace: ReturnType<typeof useRouter>["replace"];
  numberOfPages: number;
};
type Unpacked<T> = T extends (infer Item)[] ? Item : T;

const MinesInformation = ({
  minesInformationRef,
  cameraControlsRef,
  replace,
  numberOfPages,
}: MinesInformationProps) => {
  const universeAnimationSnapshot = useSnapshot(universeAnimationStore);
  const scrollContainerSnapshot = useSnapshot(scrollContainerStore);
  const [containerRef, { height }] = useElementSize();
  const exploreContainerRef = useRef<HTMLDivElement>(null);
  const { height: exploreContainerHeight } =
    useContainerDimensions(exploreContainerRef);
  const isMobile = useMediaQuery("(max-width: 1279px)");

  const YoutubeEmbed = ({ embedId }: { embedId: string }) => (
    <div style={{ width: "100%", height: "100%", marginBottom: "1rem" }}>
      <iframe
        width="100%"
        height="680"
        src={`https://www.youtube.com/embed/${embedId}`}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        title="Asteroid X"
      />
    </div>
  );

  useEffect(() => {
    scrollContainerSnapshot.setContainerHeight(height);
    scrollContainerSnapshot.setExploreContainerHeight(exploreContainerHeight);
  }, [height, exploreContainerHeight]);

  return (
    <div
      ref={containerRef}
      className={styles.container}
      style={{
        display: universeAnimationSnapshot.showFinalAnimation
          ? "none"
          : "block",
        height: `${(numberOfPages - 1) * 100}vh`,
      }}
    >
      <div ref={exploreContainerRef}>
        <div ref={minesInformationRef} className={styles.content}>
          <h1 className={styles.titleText}>Vision</h1>
          <div className={styles.visionWrapper}>
            <div className={styles.vision}>
              <YoutubeEmbed embedId="H9RfmLxFUkU" />
              {isMobile ? (
                <h2>
                  Asteroid X aims to revolutionize the traditional mining
                  finance sector using blockchain technology, particularly
                  enhancing transparency in mineral resource assessment, capital
                  flow, and risk management.
                </h2>
              ) : (
                <h2>
                  Asteroid X utilizes blockchain to tokenize mining equity,
                  ensuring transparency and accessibility. With Asteroid X,
                  users can invest in meticulously selected mining projects and
                  unlock the substantial value appreciation potential of mining
                  equity. Through strict compliance, transparent processes, and
                  professional expertise, we provide an innovative platform that
                  transforms mining investment, offering a secure and seamless
                  experience for investors.
                </h2>
              )}
              {/* <div>
                <VisionText text="Web3in Tech Lab started with three members and now has 18 members." />
                <VisionText text="Market capitalization of over 10 million AUD." />
                <VisionText text="Combining blockchain and artificial intelligence to revolutionize traditional industries." />
              </div> */}
            </div>
          </div>
          {/* <h1 className={styles.titleText}>Objective</h1>
          <div className={styles.objectiveWrapper}>
            <div className={styles.objective}>
              <q>
                Asteroid is an incubation center that offers physical asset
                digitization, pegging, and equity trading services for
                early-stage physical mining and energy (M&E) projects,
                leveraging WEB3 technology
              </q>
            </div>
          </div> */}
          {/* <h1 className={styles.titleText}>Signed Mines</h1>
          <div className={styles.mineInformation}>
            {minesDetails.map((mine) => (
              <MineInformationCard
                key={mine.name}
                mine={mine}
                isMobile={isMobile}
              />
            ))}
          </div> */}
          <TeamsInformation />
          <ProjectRoadmap />
          <Explore cameraControlsRef={cameraControlsRef} replace={replace} />
        </div>
      </div>
    </div>
  );
};

const MineInformationCard = ({
  mine,
  isMobile,
}: {
  mine: Unpacked<typeof minesDetails>;
  isMobile: boolean;
}) => {
  return (
    <div className={styles.mineInformationCard}>
      <div className={styles.descriptionWrapper}>
        <h1 className={styles.title}>{mine.name}</h1>
        {mine.description.map((text) => (
          <h2 className={styles.description} key={text}>
            <img
              src={bulletinIcon.src}
              width={24}
              height={24}
              alt="bulletin icon"
            />
            {text}
          </h2>
        ))}
      </div>
      <img
        className={styles.image}
        alt="mine image"
        src={mine.mineImages[0]}
        style={{ width: isMobile ? "100%" : undefined }}
      />
    </div>
  );
};

const VisionText = ({ text }: { text: string }) => {
  return (
    <h2>
      <img src={bulletinIcon.src} width={24} height={24} alt="bulletin icon" />
      {text}
    </h2>
  );
};

export default MinesInformation;
