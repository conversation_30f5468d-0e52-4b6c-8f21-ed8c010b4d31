import { useEffect, useState } from "react";
import styles from "./RightContents.module.scss";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import arrowDown from "@/assets/icons/bottomNavigation/arrowDown.png";
import currencyIcon from "@/assets/icons/bottomNavigation/currencyIcon.png";
import * as Select from "@radix-ui/react-select";
import { AnimatePresence, motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import { useSnapshot } from "valtio";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useDisconnect,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
  useContractReads,
  useSwitchNetwork,
} from "wagmi";
import { showCustom, showError, showSuccess } from "@/lib/notification";
import { extractErrorReason } from "@/utils/errorHandling";
import {
  BaseError,
  parseEther,
  formatEther,
  ContractFunctionExecutionError,
} from "viem";
import NetworkSelect from "./NetworkSelect/NetworkSelect";
import { networkConfigs } from "@/constants/networkConfigs";
import doubleRing from "@/assets/icons/bottomNavigation/doubleRing.svg";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { confettiStore } from "@/stores/confetti";
import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import PurchaseDetailsModal from "@/components/MainPage/PurchaseDetailsModal/PurchaseDetailsModal";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { launchPadAsteroidABI } from "@/constants/abis/LaunchPadAsteroidABI";
import { bigint } from "zod";
import { usdtABI } from "@/constants/abis/UsdtABI";
import variables from "@/styles/variables.module.scss";
import { rewardAsteroidXAbi } from "@/constants/abis/RewardAsteroidXABI";

const RightContents = () => {
  // for demo app, change to get the total supply on-chain
  const TOTAL_NFTS_SUPPLY = 20;
  const currencySelection: Record<string, string> = { usd: "USD", aud: "AUD" };
  const [selectedCurrency, setSelectedCurrency] = useState("usd");
  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [usdtAddress, setUsdtAddress] = useState<`0x${string}`>("0x");
  const [rewardAddress, setRewardAddress] = useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const confettiSnapshot = useSnapshot(confettiStore);
  const [allowance, setAllowance] = useState(false);
  const [price, setPrice] = useState(0);
  const [errorPurchasedMessage, setErrorPurchasedMessage] = useState("");
  const [specialEventErrorMessage, setSpecialEventErrorMessage] = useState("");
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();
  const { disconnect } = useDisconnect({
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      showSuccess("Wallet disconnected");
    },
  });
  const { chains, error, isLoading, pendingChainId, switchNetwork } =
    useSwitchNetwork({
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
      },
      onSuccess: () => {
        showSuccess("Switched to supported network");
      },
    });

  // 新增函数：循环切换网络
  const handleSwitchNetwork = () => {
    if (!chain || !switchNetwork || chains.length <= 1) return;

    // 查找当前链在chains数组中的位置
    const currentIndex = chains.findIndex((c) => c.id === chain.id);

    // 确定下一个要切换的网络
    let nextIndex = (currentIndex + 1) % chains.length;

    // 切换到下一个网络
    switchNetwork(chains[nextIndex].id);
  };

  // 新增函数：显示网络选择菜单
  const [showNetworkMenu, setShowNetworkMenu] = useState(false);

  const handleToggleNetworkMenu = () => {
    setShowNetworkMenu(!showNetworkMenu);
  };

  const { data: minePrice } = useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "tokenMinAmounts",
    args: [BigInt(asteroidMineNumber)],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      asteroidAddress !== "0x" &&
      asteroidMineNumber !== 0,
  });

  const { data: allowanceBalance } = useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "allowance",
    args: [address ?? "0x", launchPadAddress],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      usdtAddress !== "0x" &&
      address !== undefined &&
      launchPadAddress !== "0x",
  });

  // get the price of the next NFT
  minePrice && purchaseDetailsModalSnapshot.setSinglePrice(minePrice);

  // ----------------------------------------------------------------------------
  // Event only functions (can be removed if not needed)

  const { data: currentSpecialEventId } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "currentPeriodId",
    watch: true,
    enabled: !chain?.unsupported && isConnected,
  });

  const { data: SpecialEventDetails } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "periodInfo",
    args: [currentSpecialEventId ?? 0n],
    watch: true,
    enabled:
      !chain?.unsupported && isConnected && currentSpecialEventId !== undefined,
  });

  const { config: specialEventConfig, error: specialEventError } =
    usePrepareContractWrite({
      address: rewardAddress,
      abi: rewardAsteroidXAbi,
      functionName: "purchased",
      args: [
        BigInt(asteroidMineNumber),
        purchaseDetailsModalSnapshot.purchasePrice,
        "******************************************",
      ],
      value: parseEther(purchaseDetailsModalSnapshot.purchasePrice.toString()),
      onError: (error) => {
        if (error instanceof ContractFunctionExecutionError) {
          setSpecialEventErrorMessage(extractErrorReason(error));
        }
      },
      enabled:
        isConnected &&
        !chain?.unsupported &&
        asteroidMineNumber !== 0 &&
        purchaseDetailsModalSnapshot.purchasePrice !== 0n,
    });

  const {
    write: specialEventWrite,
    data: specialEventData,
    isLoading: isSpecialEventLoading,
  } = useContractWrite({
    ...specialEventConfig,
    onError: (error) => {
      if (error instanceof ContractFunctionExecutionError) {
        showError(extractErrorReason(error));
      }
    },
  });

  const { isLoading: isWaitingForSpecialEvent } = useWaitForTransaction({
    confirmations: 3,
    hash: specialEventData?.hash,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      showConfetti(specialEventData?.hash);
    },
  });

  const purchaseSpecialEvent = () => {
    if (chain?.unsupported) {
      showError("Please Switch Network");
      return;
    }
    if (Number(purchaseDetailsModalSnapshot.purchasePrice) === 0) {
      showError("Wrong purchase price");
      return;
    }

    if (SpecialEventDetails?.isPurchasable === false) {
      showError("Event is not started yet");
      return;
    }

    if (!specialEventWrite) {
      if (specialEventError) {
        showError(specialEventErrorMessage);
      }
      return;
    }

    specialEventWrite?.();
  };

  // ----------------------------------------------------------------------------

  // prepare to write contract
  const {
    config,
    refetch: refetchPurchased,
    error: errorPurchased,
  } = usePrepareContractWrite({
    address: launchPadAddress,
    abi: launchPadAsteroidABI,
    functionName: "purchased",
    args: [
      BigInt(asteroidMineNumber),
      purchaseDetailsModalSnapshot.purchasePrice,
    ],
    enabled:
      isConnected &&
      !chain?.unsupported &&
      asteroidMineNumber !== 0 &&
      purchaseDetailsModalSnapshot.purchasePrice !== 0n &&
      launchPadAddress !== "0x",
    onError: (error) => {
      setErrorPurchasedMessage(extractErrorReason(error));
    },
  });

  const {
    write,
    data,
    isLoading: isMintingNft,
  } = useContractWrite({
    ...config,
    onError: (error) => {
      showError(extractErrorReason(error));
    },
  });

  const { isLoading: isWaitingForConfirmation } = useWaitForTransaction({
    confirmations: 3,
    hash: data?.hash,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      showConfetti(data?.hash);
    },
  });

  const showConfetti = (hash: `0x${string}` | undefined) => {
    confettiSnapshot.setIsShowConfetti(true);
    purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(false);

    setTimeout(() => {
      confettiSnapshot.setIsShowConfetti(false);
      showSuccess("Congratulations! You have successfully purchased the Mine");
      showSuccess("Click to view your transaction");
      showCustom(
        <>
          {chain && !chain?.unsupported ? (
            <div className={styles.transactionHash}>
              <a
                href={`${networkConfigs[chain?.id].etherscanTxAddress}${hash}`}
                target="_blank"
              >
                Click here to view
              </a>
            </div>
          ) : null}
        </>,
      );
    }, 4500);
  };

  const purchaseNft = () => {
    if (chain?.unsupported) {
      showError("Please Switch Network");
      return;
    }
    if (Number(purchaseDetailsModalSnapshot.purchasePrice) === 0) {
      showError("Wrong purchase price");
      return;
    }
    if (!write) {
      if (errorPurchased) {
        showError(errorPurchasedMessage);
      }
      return;
    }

    write?.();
  };

  const { config: approved, refetch: refetchApproved } =
    usePrepareContractWrite({
      address: usdtAddress,
      abi: usdtABI,
      functionName: "approve",
      args: [
        launchPadAddress,
        parseEther("0xffffffffffffffffffffffffffffffff"),
      ],
      enabled:
        isConnected &&
        !chain?.unsupported &&
        launchPadAddress !== "0x" &&
        usdtAddress !== "0x" &&
        purchaseDetailsModalSnapshot.purchasePrice !== 0n,
      onError: (error) => {
        console.log(error);
        // showError((error as BaseError).shortMessage ?? error.message);
      },
    });

  const {
    write: approveWrite,
    data: approveData,
    isLoading: isApproving,
  } = useContractWrite({
    ...approved,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
  });

  const { isLoading: isWaitingForApprove } = useWaitForTransaction({
    confirmations: 3,
    hash: approveData?.hash,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: async () => {
      // setAllowance(true);
      // await refetchPurchased();
      // purchaseNft();
      // showSuccess("Successfully Approved, Press the Buy button");
      try {
        await refetchPurchased();

        // 延时后再检查 write 状态
        setTimeout(async () => {
          try {
            // 再次刷新状态
            await refetchPurchased();

            if (!write) {
              console.log("Write not ready, waiting for manual trigger");
              showSuccess(
                "USDT approved successfully. Please click 'Buy' to continue.",
              );
              return;
            }

            purchaseNft();
          } catch (error) {
            console.log("Auto purchase attempt failed:", error);
            showSuccess(
              "USDT approved successfully. Please click 'Buy' to continue.",
            );
          }
        }, 2000);
      } catch (error) {
        console.log("Refetch failed:", error);
        showSuccess(
          "USDT approved successfully. Please click 'Buy' to continue.",
        );
      }
    },
  });

  // approve zero logic
  const { config: approveZero } = usePrepareContractWrite({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "approve",
    args: [launchPadAddress, 0n],
    enabled:
      isConnected &&
      !chain?.unsupported &&
      launchPadAddress !== "0x" &&
      usdtAddress !== "0x",
    onError: (error) => {
      console.log(error);
    },
  });

  const {
    write: approveZeroWrite,
    data: approveZeroData,
    isLoading: isApprovingZero,
  } = useContractWrite({
    ...approveZero,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
  });

  const { isLoading: isWaitingForApproveZero } = useWaitForTransaction({
    confirmations: 7,
    hash: approveZeroData?.hash,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: async () => {
      await refetchApproved();
      approveUsdt();
    },
  });

  const approveZeroUsdt = () => {
    if (chain?.unsupported) {
      showError("Please Switch Network");
      return;
    }
    if (!approveZeroWrite) {
      showError("Unable to approve Mine");
      return;
    }
    approveZeroWrite?.();
  };

  const approveUsdt = () => {
    if (chain?.unsupported) {
      showError("Please Switch Network");
      return;
    }
    if (!approveWrite) {
      showError("Unable to approve Mine");
      return;
    }
    approveWrite?.();
  };

  const getSelectedMineAddress = (chainId: number) => {
    return networkConfigs[chainId].asteroidAddress;
  };

  const getUsdtAddress = (chainId: number) => {
    return networkConfigs[chainId].usdtAddress;
  };

  const getSelectedMineId = (chainId: number) => {
    const selectedMine = minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0].name;

    switch (selectedMine) {
      case minesDetails[0].name:
        return networkConfigs[chainId].assetIds.mt;
      case minesDetails[1].name:
        return networkConfigs[chainId].assetIds.matsa;
      case minesDetails[2].name:
        return networkConfigs[chainId].assetIds.zephyr;
      case minesDetails[3].name:
        return networkConfigs[chainId].assetIds.jim;
      case minesDetails[4].name:
        return networkConfigs[chainId].assetIds.pcgold;
      case minesDetails[5].name:
        return networkConfigs[chainId].assetIds.menzies;
      case minesDetails[6].name:
        return networkConfigs[chainId].assetIds.hskreward;
      default:
        break;
    }
  };

  useEffect(() => {
    if (chain?.unsupported) {
      setLaunchPadAddress("0x");
      setAsteroidAddress("0x");
      setRewardAddress("0x");
      showError("UNSUPPORTED NETWORK");
    } else {
      if (chain) {
        setLaunchPadAddress(networkConfigs[chain.id].launchPadAddress);
        setAsteroidAddress(getSelectedMineAddress(chain.id) ?? "0x");
        setUsdtAddress(getUsdtAddress(chain.id) ?? "0x");
        setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
        setRewardAddress(networkConfigs[chain.id].rewardAddress);
      }
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const loadingStatus = {
    loadingType: {
      isApproving,
      isWaitingForApprove,
      isApprovingZero,
      isWaitingForApproveZero,
      isWaitingForConfirmation,
      isMintingNft,
      isSpecialEventLoading,
      isWaitingForSpecialEvent,
    },
  };

  // useEffect(() => {
  //   if (allowanceBalance && asteroidMineNumber) {
  //     const value = formatEther(allowanceBalance);
  //     if (Number(value) === 0) {
  //       setAllowance(false);
  //     }
  //     if (Number(value) >= Number(purchaseDetailsModalSnapshot.purchasePrice)) {
  //       setAllowance(true);
  //     }
  //     // purchaseDetailsModalSnapshot.setSinglePrice(minePrice);
  //   }
  // }, [
  //   allowanceBalance,
  //   asteroidMineNumber,
  //   purchaseDetailsModalSnapshot.purchasePrice,
  // ]);

  const { data: unitPriceFromContract } = useContractRead({
    address: launchPadAddress,
    abi: launchPadAsteroidABI,
    functionName: "unitPrices",
    args: [BigInt(asteroidMineNumber)],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      launchPadAddress !== "0x" &&
      asteroidMineNumber !== 0,
  });

  return (
    <>
      <AnimatePresence>
        {purchaseDetailsModalSnapshot.isOpenPurchaseDetailsModal ? (
          <PurchaseDetailsModal
            purchaseNft={purchaseNft}
            // allowance={allowance}
            approveUsdt={approveUsdt}
            approveZeroUsdt={approveZeroUsdt}
            minePrice={minePrice}
            unitPriceFromContract={unitPriceFromContract}
            allowanceBalance={allowanceBalance}
            purchaseSpecialEvent={purchaseSpecialEvent}
            {...loadingStatus}
          />
        ) : null}
      </AnimatePresence>
      <div className={styles.container}>
        <div className={styles.headerContent}>
          {/* <Select.Root
          value={selectedCurrency}
          onValueChange={setSelectedCurrency}
        >
          <Select.Trigger className={styles.selectTrigger}>
            <Select.Value aria-label={selectedCurrency} asChild>
              <div className={styles.selectValue}>
                <img src={currencyIcon.src} alt="currency icon" />
                {currencySelection[selectedCurrency]}
              </div>
            </Select.Value>
            <Select.Icon>
              <img src={arrowDown.src} alt="arrow down icon" />
            </Select.Icon>
          </Select.Trigger>
          <Select.Portal>
            <Select.Content className={styles.selectContent}>
              <Select.Viewport>
                <Select.Item value="usd" className={styles.selectItem}>
                  <Select.ItemText>USD</Select.ItemText>
                  <Select.ItemIndicator>
                    <img src={currencyIcon.src} alt="currency icon" />
                  </Select.ItemIndicator>
                </Select.Item>
                <Select.Item value="aud" className={styles.selectItem}>
                  <Select.ItemText>AUD</Select.ItemText>
                  <Select.ItemIndicator>
                    <img src={currencyIcon.src} alt="currency icon" />
                  </Select.ItemIndicator>
                </Select.Item>
              </Select.Viewport>
            </Select.Content>
          </Select.Portal>
        </Select.Root> */}
          <div>
            <h1 className={styles.title}>Purchase Selected</h1>
            <h2 className={styles.subtitle}>Buy and pay</h2>
          </div>
          {/* <img src={dotDotDot.src} alt="dot dot dot icon" /> */}
        </div>

        <div className={styles.loginDetails}>
          <div className={styles.detailsWrapper}>
            <h1 className={styles.walletAddress}>Wallet:</h1>
            {address ? (
              <>
                <span>{address}</span>
                <div onClick={() => disconnect()}>Logout</div>
              </>
            ) : (
              <span>N/A</span>
            )}
          </div>
          <div className={styles.detailsWrapper}>
            <h1 className={styles.blockchainNetwork}>Network:</h1>
            {chain ? (
              <span
                style={{ textTransform: "uppercase", position: "relative" }}
              >
                {chain?.name ?? chain?.id}
                <span>
                  {" "}
                  {chain.unsupported ? "(UNSUPPORTED NETWORK)" : null}
                  {chain.unsupported ? (
                    <span onClick={() => switchNetwork?.(chains[0].id)}>
                      Switch Network{" "}
                    </span>
                  ) : null}
                  {!chain.unsupported ? (
                    <span style={{ position: "relative" }}>
                      <span
                        onClick={handleToggleNetworkMenu}
                        style={{ cursor: "pointer", color: "#f44336" }}
                      >
                        Switch Network{" "}
                      </span>
                      {showNetworkMenu && (
                        <div className={styles.networkMenu}>
                          {chains.map((x) => (
                            <div
                              key={x.id}
                              className={styles.networkMenuItem}
                              onClick={() => {
                                switchNetwork?.(x.id);
                                setShowNetworkMenu(false);
                              }}
                            >
                              {x.name}{" "}
                              {x.id === chain.id ? (
                                <span style={{ color: "#4CAF50" }}>
                                  (Current)
                                </span>
                              ) : (
                                ""
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </span>
                  ) : null}
                </span>
              </span>
            ) : (
              <span style={{ textTransform: "uppercase" }}>N/A</span>
            )}
          </div>

          {isConnected ? (
            <>
              {isMintingNft ||
              isWaitingForConfirmation ||
              isApproving ||
              isWaitingForApprove ||
              isApprovingZero ||
              isWaitingForApproveZero ||
              isSpecialEventLoading ||
              isWaitingForSpecialEvent ? (
                <motion.button
                  className={styles.loginButton}
                  disabled
                  style={{ cursor: "wait" }}
                >
                  <img
                    src={doubleRing.src}
                    width={20}
                    height={20}
                    alt="loading icon"
                  />
                  {"  "}
                  Processing...
                </motion.button>
              ) : (
                <motion.button
                  className={styles.loginButton}
                  whileTap={buttonEffect.tap}
                  // disabled={!write}
                  onClick={() => {
                    // showConfetti();
                    // purchaseNft();
                    if (chain && chain.unsupported) {
                      showError("Please Switch Network");
                      return;
                    }
                    if (
                      chain &&
                      selectedMine.supportedNetwork.includes(chain?.id)
                    ) {
                      purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(
                        true,
                      );
                    } else {
                      switchNetwork?.(
                        chain?.testnet
                          ? selectedMine.supportedNetwork[0]
                          : selectedMine.supportedNetwork[1],
                      );
                    }
                  }}
                >
                  Purchase This Mine
                </motion.button>
              )}
            </>
          ) : (
            <motion.div
              className={styles.loginButton}
              whileTap={buttonEffect.tap}
              onClick={() => {
                connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
              }}
            >
              Log in <span>&nbsp;or&nbsp;</span> Register Now
            </motion.div>
          )}
        </div>
      </div>
    </>
  );
};

export default RightContents;
