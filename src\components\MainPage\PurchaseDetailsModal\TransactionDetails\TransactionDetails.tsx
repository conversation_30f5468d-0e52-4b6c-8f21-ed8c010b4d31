import { Dispatch, SetStateAction, useState } from "react";
import styles from "./TransactionDetails.module.scss";
import CountUp from "react-countup";
import usePurchaseHistory from "../../LaunchSection/PurchaseHistory/hooks/usePurchaseHistory";
import { minesDetails } from "@/constants/mineDetails";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { useNetwork } from "wagmi";

type TransactionDetailsProps = {
  selectedPurchaseUnits: { value: bigint | undefined; label: string };
  setIsSoldOut: Dispatch<SetStateAction<boolean>>;
};

const TransactionDetails = ({
  selectedPurchaseUnits,
  setIsSoldOut,
}: TransactionDetailsProps) => {
  // reuse areaBought because it serve the same logic
  // naming may not be make sense
  const { areaBought } = usePurchaseHistory();

  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const blockchainMineData = areaBought.filter(
    (area) => area.mineDetails.name === selectedMine.name,
  )[0];
  const isSpecialEvent = mineCardSnapshot.selectedMine === "HSK Reward";

  const shareSold =
    Number(blockchainMineData?.tokenDetails?.soldAmount) /
    Number(blockchainMineData?.tokenDetails?.minAmount);

  const shareToPurchase = Number(selectedPurchaseUnits?.value);
  // Number(selectedPurchaseUnits?.value) /
  // Number(blockchainMineData?.tokenDetails?.minAmount);
  const nftToPurchase =
    Number(selectedPurchaseUnits?.value) /
    Number(blockchainMineData?.tokenDetails?.minAmount);

  const tokenSupply = Number(blockchainMineData?.tokenSupply);

  if (shareSold === tokenSupply) {
    setIsSoldOut(true);
  }

  const { chain } = useNetwork();
  const isHashkeyNetwork = ["133", "177"].includes(chain?.id?.toString() ?? "");
  return (
    <div className={styles.deepDetailsWrapper}>
      <div className={styles.deepDetailsContainer}>
        <div className={styles.deepDetails}>
          <h1>CURRENT PURCHASE</h1>
          <h2 className={styles.shareToPurchase}>
            {selectedPurchaseUnits.value?.toString()} <span>SHARES</span>
          </h2>
        </div>
        <div className={styles.deepDetails}>
          <h1>FUNDING TARGET</h1>
          <h2 className={styles.totalShare}>
            $
            <CountUp
              end={selectedMine.ipoData.fundingTarget}
              // end={Number(blockchainMineData?.tokenDetails?.targetFund)}
              duration={3}
            />
          </h2>
        </div>
        <div className={styles.deepDetails}>
          <h1>TOTAL TOKENIZED SHARES</h1>
          <h2 className={styles.totalShare}>
            <CountUp
              end={Math.floor(selectedMine.ipoData.tokenizedShares / 100)}
              // end={Number(blockchainMineData?.tokenDetails?.totalSupply)}
              duration={3}
            />
            <span style={{ color: "rgb(208, 208, 13)", fontSize: "1rem" }}>
              {" "}
              ITEMS
            </span>
          </h2>
        </div>
        <div className={styles.deepDetails}>
          <h1>ASTEROIDX OFFERING</h1>
          <h2 className={styles.totalShare}>
            <CountUp end={selectedMine.ipoData.totalNfts} duration={3} />
            <span style={{ color: "rgb(208, 208, 13)", fontSize: "1rem" }}>
              {" "}
              ITEMS
            </span>
          </h2>
        </div>
        <div className={styles.deepDetails}>
          <h1>MINIMUM PURCHASE</h1>
          <h2 className={styles.shareToPurchase}>
            {/* {shareToPurchase !== Infinity
              ? shareToPurchase
              : isSpecialEvent
              ? "1"
              : "n/a"}{" "} */}
            {selectedMine.ipoData.shareOffset}{" "}
            <span>
              SHARES (1{" "}
              {/* {nftToPurchase === Infinity
                ? 1
                : nftToPurchase / selectedMine.ipoData.shareOffset}{" "} */}
              ITEM)
            </span>
          </h2>
        </div>
        <div className={styles.deepDetails}>
          <h1>NFT SOLD</h1>
          <h2 className={styles.shareSold}>
            <CountUp end={shareSold} duration={3} />
            <span style={{ color: "gray" }}>
              {" "}
              / {selectedMine.ipoData.totalNfts}
            </span>
          </h2>
        </div>
        <div className={styles.deepDetails}>
          {/* <h3>0</h3> */}
          <progress
            value={shareSold}
            max={selectedMine.ipoData.totalNfts}
            style={{ flex: 1, margin: "0rem 0rem 0.75rem" }}
          />
          {/* <h3>100</h3> */}
        </div>
        <div
          className={`${styles.deepDetails} ${styles.investorAttractiveSection}`}
        >
          <h1 className={styles.gradientText}>Expected Return</h1>
          <h2 className={styles.returnValue}>
            <CountUp end={50} duration={3} />
            %+
          </h2>
        </div>
      </div>
    </div>
  );
};

export default TransactionDetails;
