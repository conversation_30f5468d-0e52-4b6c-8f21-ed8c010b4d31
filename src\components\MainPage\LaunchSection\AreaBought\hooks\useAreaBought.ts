import { creator<PERSON><PERSON><PERSON> } from "@/constants/abis/creatorNF<PERSON>bi";
import { launchPadAbi } from "@/constants/abis/launchPadAbi";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError } from "@/lib/notification";
import { SetStateAction, useEffect, useState } from "react";
import { useAccount, useContractReads, useNetwork } from "wagmi";

type ContractList = {
  address: `0x${string}`;
  abi: typeof creatorNFTAbi;
  functionName: string;
  args: BigInt[];
};

export const useAreaBought = () => {
  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [creatorNFTAddress, setCreatorNFTAddress] = useState<`0x${string}`[]>(
    [],
  );
  const [contractListMt, setContractListMt] = useState<ContractList[]>([]);
  const [contractListMatsa, setContractListMatsa] = useState<ContractList[]>(
    [],
  );
  const [contractListZephyr, setContractListZephyr] = useState<ContractList[]>(
    [],
  );

  const [mineOwnedStatus, setMineOwnedStatus] = useState<boolean[]>([]);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();

  const { data: nftSold } = useContractReads({
    contracts: [
      {
        address: launchPadAddress,
        abi: launchPadAbi,
        functionName: "getNftSold",
        args: [creatorNFTAddress[0]],
      },
      {
        address: launchPadAddress,
        abi: launchPadAbi,
        functionName: "getNftSold",
        args: [creatorNFTAddress[1]],
      },
      {
        address: launchPadAddress,
        abi: launchPadAbi,
        functionName: "getNftSold",
        args: [creatorNFTAddress[2]],
      },
    ],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      creatorNFTAddress.length > 0 &&
      launchPadAddress !== "0x",
  });

  const { data: buyersMt } = useContractReads({
    contracts: contractListMt,
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      creatorNFTAddress[0] &&
      contractListMt.length > 0,
  });
  const { data: buyersMatsa } = useContractReads({
    contracts: contractListMatsa,
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      creatorNFTAddress[1] &&
      contractListMatsa.length > 0,
  });
  const { data: buyersZephyr } = useContractReads({
    contracts: contractListZephyr,
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      creatorNFTAddress[2] &&
      contractListZephyr.length > 0,
  });

  const getAllMinesAddress = (chainId: number) => [
    networkConfigs[chainId].asteroidAddress,
  ];

  const createContractList = (
    nftSold: bigint,
    address: `0x${string}`,
    setContractList: (value: SetStateAction<ContractList[]>) => void,
  ) => {
    const tempContractList: ContractList[] = [];
    [...Array(Number(nftSold))].map((_, index) => {
      tempContractList.push({
        address,
        abi: creatorNFTAbi,
        functionName: "ownerOf",
        args: [BigInt(index)],
      });
    });
    setContractList(tempContractList);
  };

  useEffect(() => {
    if (chain?.unsupported) {
      setLaunchPadAddress("0x");
      setCreatorNFTAddress([]);
    } else {
      if (chain) {
        setLaunchPadAddress(networkConfigs[chain.id].launchPadAddress);
        setCreatorNFTAddress(getAllMinesAddress(chain.id));
      }
    }
  }, [chain]);

  useEffect(() => {
    nftSold?.map((nft, index) => {
      if (nft.status === "success") {
        switch (index) {
          case 0: {
            createContractList(
              // @ts-ignore
              nft.result,
              creatorNFTAddress[0],
              setContractListMt,
            );
          }
          case 1: {
            createContractList(
              // @ts-ignore
              nft.result,
              creatorNFTAddress[1],
              setContractListMatsa,
            );
          }
          case 2: {
            createContractList(
              // @ts-ignore
              nft.result,
              creatorNFTAddress[2],
              setContractListZephyr,
            );
          }
        }
      }
    });
  }, [nftSold]);

  useEffect(() => {
    if (buyersMt && buyersMatsa && buyersZephyr && address) {
      const tempBuyersMt = buyersMt.map((buyer) => {
        if (buyer.status === "success") {
          return buyer.result;
        }
      });
      const tempBuyersMatsa = buyersMatsa.map((buyer) => {
        if (buyer.status === "success") {
          return buyer.result;
        }
      });
      const tempBuyersZephyr = buyersZephyr.map((buyer) => {
        if (buyer.status === "success") {
          return buyer.result;
        }
      });
      const tempStatus = [
        tempBuyersMt.includes(address),
        tempBuyersMatsa.includes(address),
        tempBuyersZephyr.includes(address),
      ];

      setMineOwnedStatus(tempStatus);
    }
  }, [address, buyersMt, buyersMatsa, buyersZephyr]);

  return { mineOwnedStatus };
};
