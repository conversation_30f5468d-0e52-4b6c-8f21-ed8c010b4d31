import bnbIcon from "@/assets/icons/bottomNavigation/bnbIcon.png";
import polygonIcon from "@/assets/icons/bottomNavigation/polygonIcon.png";
import ethereumIcon from "@/assets/icons/bottomNavigation/ethereumIcon.png";

type AssetIds = {
  mt: number;
  matsa: number;
  zephyr: number;
  jim: number;
  pcgold: number;
  menzies: number;
  hskreward: number;
  redpacket: number;
};

type NetworkConfigs = {
  [key: number]: {
    launchPadAddress: `0x${string}`;
    asteroidAddress: `0x${string}`;
    usdtAddress: `0x${string}`;
    airdropAddress: `0x${string}`;
    assetIds: AssetIds;
    currencyUnit: "HSK" | "USDT";
    icon: string;
    etherscanAddress?: string;
    etherscanTxAddress?: string;
    faucet?: string;
    symbol: string;
    rewardAddress: `0x${string}`;
    redpacketAddress?: `0x${string}`;
    marketplaceAddress: `0x${string}`;
    privateSaleAddress: `0x${string}`;
  };
};

export const networkConfigs: NetworkConfigs = {
  // mainnet
  1: {
    launchPadAddress: "0x",
    asteroidAddress: "0x",
    usdtAddress: "0x",
    airdropAddress: "0x",
    rewardAddress: "0x",
    marketplaceAddress: "0x",
    privateSaleAddress: "0x",
    assetIds: {
      mt: 0,
      matsa: 0,
      zephyr: 0,
      jim: 0,
      pcgold: 0,
      menzies: 0,
      hskreward: 0,
      redpacket: 0,
    },
    currencyUnit: "USDT",
    icon: ethereumIcon.src,
    etherscanAddress: "https://etherscan.io/address/",
    etherscanTxAddress: "https://etherscan.io/tx/",
    symbol: "ETH",
  },
  // hsk mainnet
  177: {
    launchPadAddress: "******************************************",
    asteroidAddress: "******************************************",
    usdtAddress: "******************************************",
    airdropAddress: "******************************************",
    rewardAddress: "******************************************",
    redpacketAddress: "******************************************",
    marketplaceAddress: "0x",
    privateSaleAddress: "0x",
    assetIds: {
      mt: 100,
      matsa: 101,
      zephyr: 102,
      jim: 103,
      pcgold: 1000,
      menzies: 105,
      hskreward: 999,
      redpacket: 2025,
    },
    currencyUnit: "HSK",
    icon: ethereumIcon.src,
    etherscanAddress: "https://explorer.hsk.xyz/address/",
    etherscanTxAddress: "https://explorer.hsk.xyz/tx/",
    faucet: "https://hsk.xyz/faucet",
    symbol: "HSK",
  },
  // sepolia
  11155111: {
    launchPadAddress: "******************************************",
    asteroidAddress: "******************************************",
    usdtAddress: "******************************************",
    airdropAddress: "******************************************",
    rewardAddress: "0x",
    marketplaceAddress: "0x",
    privateSaleAddress: "0x",
    assetIds: {
      mt: 100,
      matsa: 101,
      zephyr: 102,
      jim: 103,
      pcgold: 104,
      menzies: 105,
      hskreward: 999,
      redpacket: 0,
    },
    currencyUnit: "USDT",
    icon: ethereumIcon.src,
    etherscanAddress: "https://sepolia.etherscan.io/address/",
    etherscanTxAddress: "https://sepolia.etherscan.io/tx/",
    faucet: "https://cloud.google.com/application/web3/faucet/ethereum/sepolia",
    symbol: "ETH",
  },
  // hsk testnet
  133: {
    launchPadAddress: "******************************************",
    asteroidAddress: "******************************************",
    usdtAddress: "******************************************",
    airdropAddress: "******************************************",
    rewardAddress: "******************************************",
    redpacketAddress: "******************************************",
    marketplaceAddress: "******************************************",
    privateSaleAddress: "******************************************",
    assetIds: {
      mt: 100,
      matsa: 101,
      zephyr: 102,
      jim: 103,
      pcgold: 106,
      menzies: 105,
      hskreward: 999,
      redpacket: 2025,
    },
    currencyUnit: "USDT",
    icon: ethereumIcon.src,
    etherscanAddress:
      "https://hashkeychain-testnet-explorer.alt.technology/address/",
    etherscanTxAddress:
      "https://hashkeychain-testnet-explorer.alt.technology/tx/",
    faucet: "https://hsk.xyz/faucet",
    symbol: "HSK",
  },
  // bnb testnet
  97: {
    launchPadAddress: "******************************************",
    asteroidAddress: "******************************************",
    usdtAddress: "******************************************",
    airdropAddress: "******************************************",
    rewardAddress: "0x",
    marketplaceAddress: "******************************************",
    privateSaleAddress: "0x",
    assetIds: {
      mt: 100,
      matsa: 101,
      zephyr: 102,
      jim: 0,
      pcgold: 104,
      menzies: 0,
      hskreward: 0,
      redpacket: 0,
    },
    currencyUnit: "USDT",
    icon: bnbIcon.src,
    etherscanAddress: "https://testnet.bscscan.com/address/",
    etherscanTxAddress: "https://testnet.bscscan.com/tx/",
    faucet: "https://www.bnbchain.org/en/testnet-faucet",
    symbol: "BNB",
  },
  // bnb mainnet
  56: {
    launchPadAddress: "******************************************",
    asteroidAddress: "******************************************",
    usdtAddress: "******************************************",
    airdropAddress: "0x",
    rewardAddress: "0x",
    marketplaceAddress: "0x",
    privateSaleAddress: "0x",
    assetIds: {
      mt: 0,
      matsa: 0,
      zephyr: 0,
      jim: 0,
      pcgold: 1000,
      menzies: 0,
      hskreward: 0,
      redpacket: 0,
    },
    currencyUnit: "USDT",
    icon: bnbIcon.src,
    etherscanAddress: "https://bscscan.com/address/",
    etherscanTxAddress: "https://bscscan.com/tx/",
    symbol: "BNB",
  },
  // mumbai
  80001: {
    launchPadAddress: "******************************************",
    asteroidAddress: "******************************************",
    usdtAddress: "******************************************",
    airdropAddress: "******************************************",
    rewardAddress: "0x",
    marketplaceAddress: "0x",
    privateSaleAddress: "0x",
    assetIds: {
      mt: 100,
      matsa: 101,
      zephyr: 102,
      jim: 0,
      pcgold: 0,
      menzies: 0,
      hskreward: 0,
      redpacket: 0,
    },
    currencyUnit: "USDT",
    icon: polygonIcon.src,
    symbol: "MATIC",
  },
  // polygon
  137: {
    launchPadAddress: "******************************************",
    asteroidAddress: "******************************************",
    usdtAddress: "******************************************",
    airdropAddress: "0x",
    rewardAddress: "0x",
    marketplaceAddress: "0x",
    privateSaleAddress: "0x",
    assetIds: {
      mt: 100,
      matsa: 101,
      zephyr: 102,
      jim: 0,
      pcgold: 0,
      menzies: 0,
      hskreward: 0,
      redpacket: 0,
    },
    currencyUnit: "USDT",
    icon: polygonIcon.src,
    symbol: "MATIC",
  },
};
