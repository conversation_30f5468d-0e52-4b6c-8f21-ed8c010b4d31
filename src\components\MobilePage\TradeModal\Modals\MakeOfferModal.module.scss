@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(10deg) rotateY(0deg);
$download-rotation-angle: rotateX(6deg) rotateY(0deg);

.wrapper {
  position: fixed;
  top: 52%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px; /* 可以根据实际需求调整最大宽度 */
  height: 85%;
  max-height: 600px; /* 可以根据实际需求调整最大高度 */
  z-index: 1000;
  background: $color-black-transparent;
  backdrop-filter: blur(10px);
  // mask-image: linear-gradient(to bottom, black 85%, transparent);
  padding: $padding-md;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    // background: linear-gradient(
    //   to bottom,
    //   $color-primary,
    //   $color-black-transparent
    // );
    box-shadow: 0 0 10rem 10rem $color-primary;

    clip-path: polygon(
      0 0,
      calc(100% - 32px) 0,
      100% 32px,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      1px calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 1px) calc(32px + 0.41px),
      calc(100% - 32px - 0.41px) 1px,
      1px 1px
    );
  }

  .header {
    font-family: "Garalama", sans-serif;
    font-weight: $font-weight-extralight;
    letter-spacing: 1px;
    width: 100%;
    color: #00e0ff;
  }
}

.contentWrapper {
  width: 100%;
  height: 100%;
  padding: $padding-sm 0;
  @include col-center;
  gap: 1rem;

  .scrollArea {
    width: 100%;
    height: 100%;
    overflow: scroll;
    scroll-behavior: smooth;
    display: flex; /* 新增 */
    flex-direction: column; /* 新增 */
    gap: 1rem; /* 现在会生效 */

    &::-webkit-scrollbar {
      display: none;
    }
  }
}
.headerWrapper {
  @include row-between;
  // margin-bottom: 1rem;
  gap: 1rem;
  
  .closeButton {
    margin-top: 0.5rem;
  }
}

.derlordInfo {
  h3 {
    color: #00e0ff;
    font-size: 24px;
  }

  span {
    display: block;
    color: #00e0ff;
  }

  p {
    color: #00e0ff;
    font-size: 14px;
  }
}

.priceInput {
  label {
    display: block;
    color: #00e0ff;
    margin-bottom: 8px;
  }

  input {
    border-radius: 0.5rem;
    width: 100%;
    background: rgba(0, 224, 255, 0.1);
    border: 1px solid $color-primary-contrast;
    padding: 12px;
    color: #00e0ff;
    margin-bottom: 8px;
    font-size: 16px;

    &::placeholder {
      color: #00e0ff;
      opacity: 0.5;
    }
  }
}

.totalOffer {
  font-size: 12px;
  font-family: "Garalama", sans-serif;
  font-weight: $font-weight-extralight;
  letter-spacing: 1px;
  color: gray;
  text-align: right;
}

.quantitySection {
  @include row-between;
  .quantityLabel {
    display: flex;
    justify-content: space-between;
    color: #00e0ff;
    flex-direction: column;

    .available {
      font-size: 14px;
      color: gray;
    }
  }

  .quantityControls {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background: rgba(0, 224, 255, 0.2);
    border: 1px solid $color-primary-contrast;
    // padding: $padding-sm;
    border-radius: 0.5rem;

    input {
      border-radius: 0.5rem;
      width: 100%;
      background: rgba(0, 224, 255, 0.1);
      border: none;
      padding: 12px;
      color: #00e0ff;
      font-size: 16px;
  
      &::placeholder {
        color: #00e0ff;
        opacity: 0.5;
      }
    }


    .quantityBtn {
      color: #00e0ff;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 20px;

      &:hover {
        color: white;
      }
    }

    span {
      color: #00e0ff;
    }
  }
}

.durationSection {
  span {
    display: block;
    color: #00e0ff;
    margin-bottom: 8px;
  }

  .durationControls {
    display: flex;
    gap: 10px;

    select {
      border-radius: 0.5rem;
      background: rgba(0, 224, 255, 0.1);
      border: 1px solid $color-primary-contrast;
      color: #00e0ff;
      padding: 2px;
    }

    .dateDisplay {
      @include row-between;
      // gap: 1rem;
      width: 100%;
      border-radius: 0.5rem;
      flex: 1;
      background: rgba(0, 224, 255, 0.1);
      border: 1px solid $color-primary-contrast;
      color: #00e0ff;
      padding: $padding-sm;
    }
  }
}

.completeButton {
  border-radius: 0.5rem;
  width: 100%;
  margin: 1rem 0;
  background: rgba(0, 224, 255, 0.2);
  border: 1px solid $color-primary-contrast;
  color: #00e0ff;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 224, 255, 0.3);
  }
}
