import { FC, useEffect, useState } from "react";
import styles from "./MakeOfferModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import useMakeOffer from "../../hooks/useMakeOffer";
import { showError } from "@/lib/notification";

interface MakeOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MakeOfferModal: FC<MakeOfferModalProps> = ({ isOpen, onClose }) => {
  const [duration, setDuration] = useState("1 MONTH");
  const [expirationDate, setExpirationDate] = useState("");
  const [inputValue, setInputValue] = useState("");
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const {
    MAX_OFFER_AMOUNT,
    numberOfItem,
    setNumberOfItem,
    pricePerToken,
    setPricePerToken,
    totalEth,
    setExpirationDateUnixTime,
    makeOffer,
    isMakingOffer,
    isWaitingForMakeOffer,
  } = useMakeOffer(onClose);

  const getUnixTimestamp = (isoDateString: string): number => {
    if (!isoDateString) return 0;
    return Math.floor(new Date(isoDateString).getTime() / 1000);
  };

  useEffect(() => {
    const calculateExpirationDate = () => {
      const now = new Date();
      let futureDate = new Date(now);

      if (duration === "1 MONTH") {
        futureDate.setMonth(now.getMonth() + 1);
      } else if (duration === "3 MONTHS") {
        futureDate.setMonth(now.getMonth() + 3);
      } else if (duration === "6 MONTHS") {
        futureDate.setMonth(now.getMonth() + 6);
      }

      const formattedDate = futureDate.toISOString().slice(0, 16);
      setExpirationDate(formattedDate);
      setExpirationDateUnixTime(getUnixTimestamp(formattedDate));
    };

    calculateExpirationDate();
  }, [duration]);

  const handleMakeOffer = () => {
    // Check if amount is valid
    if (pricePerToken <= 0) {
      showError("Please enter a valid price amount");
      return;
    }
    // Check if number of items is valid
    if (numberOfItem <= 0) {
      showError("Please enter a valid number of items");
      return;
    }
    // Check if number of items exceeds available NFTs
    // if (numberOfItem > Number(avaliableNfts)) {
    //   showError("Number of items cannot exceed available NFTs");
    //   return;
    // }

    try {
      makeOffer();
    } catch (error) {
      console.error("Error creating listing:", error);
      showError("Error creating listing");
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalFrame}>
        <div className={styles.closeButton} onClick={onClose}>
          <img src={crossIcon.src} alt="cross icon" />
        </div>

        <div className={styles.contentWrapper}>
          <h2 className={styles.header}>MAKE AN OFFER</h2>
          <div className={styles.scrollArea}>
            <div className={styles.contentFrame}>
              <div className={styles.derlordInfo}>
                <h3>{selectedMine.name}</h3>
                {/* <span>0.45 ETH</span> */}
              </div>

              <div className={styles.priceInput}>
                <label>PRICE</label>
                <input
                  type="text" // 使用text而不是number以获得更好的控制
                  placeholder="Price per token"
                  value={inputValue}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    // 仅允许数字和小数点
                    if (
                      newValue === "" ||
                      newValue === "0" ||
                      /^[1-9][0-9]*\.?[0-9]{0,2}$/.test(newValue) ||
                      /^0\.[0-9]{0,2}$/.test(newValue)
                    ) {
                      setInputValue(newValue);

                      // 更新实际数值
                      if (newValue === "" || newValue === ".") {
                        setPricePerToken(0);
                      } else {
                        const numValue = parseFloat(newValue);
                        setPricePerToken(numValue);
                      }
                    }
                  }}
                />
                <div className={styles.totalOffer}>
                  TOTAL OFFER AMOUNT: {totalEth} {selectedMine.currency}
                </div>
              </div>

              <div className={styles.quantitySection}>
                <div className={styles.quantityLabel}>
                  <span>QUANTITY</span>
                  <span className={styles.available}>
                    MAX {MAX_OFFER_AMOUNT} NFTs
                  </span>
                </div>
                <div className={styles.quantityControls}>
                  {/* <button
                    className={styles.quantityBtn}
                    onClick={() =>
                      setNumberOfItem((prev) => (prev > 0 ? prev - 1 : prev))
                    }
                  >
                    -
                  </button>
                  <span>{numberOfItem}</span>
                  <button
                    className={styles.quantityBtn}
                    onClick={() =>
                      setNumberOfItem((prev) =>
                        prev < MAX_OFFER_AMOUNT ? prev + 1 : prev,
                      )
                    }
                  >
                    +
                  </button> */}
                  <input
                    type="number"
                    placeholder="#Items"
                    min={1}
                    max={MAX_OFFER_AMOUNT}
                    step={1}
                    value={numberOfItem === 0 ? "" : numberOfItem}
                    onChange={(e) => {
                      if (e.target.value === "") {
                        setNumberOfItem(0);
                      } else {
                        // 获取输入值并转换为整数
                        let value = parseInt(e.target.value, 10);

                        // 确保是有效的数字（非NaN）
                        if (isNaN(value)) value = 0;

                        // 限制在1到200之间
                        if (value < 0) value = 0;
                        if (value > MAX_OFFER_AMOUNT) value = MAX_OFFER_AMOUNT;

                        setNumberOfItem(value);
                      }
                    }}
                    // 防止用户输入小数
                    onKeyDown={(e) => {
                      // 阻止小数点、逗号、负号、加号
                      if (
                        e.key === "." ||
                        e.key === "," ||
                        e.key === "-" ||
                        e.key === "+" ||
                        e.key === "e"
                      ) {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>

              <div className={styles.durationSection}>
                <span>DURATION</span>
                <div className={styles.durationControls}>
                  <select
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                  >
                    <option value="1 MONTH">1 MONTH</option>
                    <option value="3 MONTHS">3 MONTHS</option>
                    <option value="6 MONTHS">6 MONTHS</option>
                  </select>
                  <input
                    className={styles.dateDisplay}
                    type="datetime-local"
                    value={expirationDate}
                    readOnly
                  />
                </div>
              </div>
              <button
                className={styles.completeButton}
                onClick={handleMakeOffer}
                disabled={isWaitingForMakeOffer || isMakingOffer}
              >
                {isMakingOffer || isWaitingForMakeOffer ? (
                  <div className={styles.loadingIndicator}>
                    <span className={styles.loader}></span>
                    Processing...
                  </div>
                ) : (
                  "COMPLETE OFFER"
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MakeOfferModal;
