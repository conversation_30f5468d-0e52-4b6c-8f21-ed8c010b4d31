import { FC } from "react";
import styles from "./BuyListingModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { formatEther } from "viem";
import useCancelOffer from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useCancelOffer";
import useActiveListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useActiveListing";
import usePurchaseAllListingAmount from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/usePurchaseAllListingAmount";
import useApprove from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useApprove";
import { useAccount, useNetwork } from "wagmi";
import useCancelListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useCancelListing";
import { networkConfigs } from "@/constants/networkConfigs";

interface BuyListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  allActiveListingSortedByPriceFiltered: ReturnType<
    typeof useActiveListing
  >["allActiveListingSortedByPriceFiltered"];
}

const trimWalletAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

const BuyListingModal: FC<BuyListingModalProps> = ({
  isOpen,
  onClose,
  allActiveListingSortedByPriceFiltered,
}) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { formatUnixTimestamp } = useActiveListing();
  const {
    isPurchasingListing,
    isWaitingForPurchaseListing,
    purchaseListing,
    setAmount,
    setListingId,
    setPricePerToken,
    listingId,
  } = usePurchaseAllListingAmount();
  const { address } = useAccount();
  const { isApprovedForAll } = useApprove();

  const {
    cancelListing,
    isCancelingListing,
    isWaitingForCancelListing,
    setSelectedListingId,
    selectedListingId,
  } = useCancelListing();
  const { chain } = useNetwork();

  const handleBuyAllAmount = () => {
    purchaseListing();
  };

  const handleMouseOver = (index: number, isOwner: boolean) => {
    if (allActiveListingSortedByPriceFiltered) {
      if (isOwner) {
        setSelectedListingId(
          allActiveListingSortedByPriceFiltered[index].listingId,
        );
      } else {
        setAmount(allActiveListingSortedByPriceFiltered[index].amount);
        setPricePerToken(
          allActiveListingSortedByPriceFiltered[index].pricePerToken,
        );
        setListingId(allActiveListingSortedByPriceFiltered[index].listingId);
      }
    }
  };

  const handleCancelListing = () => {
    cancelListing();
  };

  if (!isOpen) return null;
  if (allActiveListingSortedByPriceFiltered?.length === 0) return null;

  return (
    <div className={styles.wrapper}>
      <div className={styles.headerWrapper}>
        <h3 className={styles.header}>BUY LISTING</h3>
        <div className={styles.closeButton} onClick={onClose}>
          <img src={crossIcon.src} alt="cross icon" />
        </div>
      </div>

      <div className={styles.contentWrapper}>
        <div className={styles.scrollArea}>
          <div className={styles.contentFrame}>
            <div className={styles.derlordInfo}>
              <h3>{selectedMine.name}</h3>
            </div>
            <div className={styles.listingsTable}>
              <div className={styles.tableHeader}>
                <div>Address</div>
                <div>Price per Token</div>
                <div>Quantity</div>
                <div>Selling Price</div>
                <div>Expiration</div>
                <div>Action</div>
              </div>
              <div className={styles.tableBody}>
                {allActiveListingSortedByPriceFiltered &&
                allActiveListingSortedByPriceFiltered.length > 0 ? (
                  allActiveListingSortedByPriceFiltered.map(
                    (listing, index) => (
                      <div
                        className={`${styles.tableRow} ${
                          listing.seller === address ? styles.ownListing : ""
                        }`}
                        key={index}
                      >
                        <div>
                          <a
                            href={
                              chain &&
                              networkConfigs[chain.id].etherscanAddress +
                                listing.seller
                            }
                            target="_blank"
                            style={{ color: "#4895ef" }}
                          >
                            {trimWalletAddress(listing.seller)}
                          </a>
                        </div>
                        <div className={styles.price}>
                          {formatEther(listing.pricePerToken)}{" "}
                          {selectedMine.currency}
                        </div>
                        <div className={styles.quantity}>
                          {listing.amount.toString()}
                        </div>
                        <div className={styles.price}>
                          {formatEther(listing.pricePerToken * listing.amount)}{" "}
                          {selectedMine.currency}
                        </div>
                        <div className={styles.expiration}>
                          {formatUnixTimestamp(Number(listing.expirationTime))}
                        </div>
                        <div>
                          <button
                            className={`${styles.buyButton} ${
                              listing.seller === address
                                ? styles.cancelButton
                                : ""
                            } ${
                              isPurchasingListing ||
                              isWaitingForPurchaseListing ||
                              isCancelingListing ||
                              isWaitingForCancelListing
                                ? styles.disabled
                                : ""
                            }`}
                            onClick={() =>
                              listing.seller === address
                                ? handleCancelListing()
                                : handleBuyAllAmount()
                            }
                            onMouseEnter={() =>
                              handleMouseOver(index, listing.seller === address)
                            }
                            disabled={
                              isPurchasingListing ||
                              isWaitingForPurchaseListing ||
                              isCancelingListing ||
                              isWaitingForCancelListing ||
                              !isApprovedForAll
                            }
                            style={{
                              opacity:
                                isPurchasingListing ||
                                isWaitingForPurchaseListing ||
                                isCancelingListing ||
                                isWaitingForCancelListing ||
                                !isApprovedForAll
                                  ? 0.5
                                  : 1,
                              pointerEvents:
                                isPurchasingListing ||
                                isWaitingForPurchaseListing ||
                                isCancelingListing ||
                                isWaitingForCancelListing ||
                                !isApprovedForAll
                                  ? "none"
                                  : "auto",
                            }}
                          >
                            {/* Show Processing... when the specific action is happening on this listing */}
                            {((isPurchasingListing ||
                              isWaitingForPurchaseListing) &&
                              listingId === listing.listingId &&
                              listing.seller !== address) ||
                            ((isCancelingListing ||
                              isWaitingForCancelListing) &&
                              listing.listingId === selectedListingId &&
                              listing.seller === address)
                              ? "Processing..."
                              : listing.seller === address
                              ? "CANCEL LISTING"
                              : "BUY"}
                          </button>
                        </div>
                      </div>
                    ),
                  )
                ) : (
                  <div className={styles.noListings}>
                    No active listings available
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyListingModal;
