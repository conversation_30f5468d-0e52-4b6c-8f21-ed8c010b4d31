import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { mineCardStore } from "@/stores/mineCard";
import { useEffect, useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError } from "viem";
import { minesDetails } from "@/constants/mineDetails";

type TOffer = {
  offerId: bigint;
  buyer: `0x${string}`;
  tokenId: bigint;
  amount: bigint;
  pricePerToken: bigint;
  expirationTime: bigint;
  active: boolean;
};

const useCancelOffer = (onClose?: () => void) => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [limit, setLimit] = useState(50n);
  const [allActiveOfferSortedByPrice, setAllActiveOfferSortedByPrice] =
    useState<TOffer[]>([]);
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [selectedOfferId, setSelectedOfferId] = useState(0n);
  const [isCancelingOffer, setIsCancelingOffer] = useState(false);
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const { getSelectedMineId } = useMineTokenId();
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const formatUnixTimestamp = (timestamp: number): string => {
    if (!timestamp) return "";

    const date = new Date(timestamp * 1000);
    return date.toLocaleString("en-AU", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketplaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const { data: allActiveOffer } = useContractRead({
    address: marketplaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getActiveOffersPaginated",
    args: [BigInt(asteroidMineNumber), 0n, limit],
    enabled:
      isConnected && marketplaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
    onSuccess: (data) => {
      setLimit(data[1]);
    },
  });

  useEffect(() => {
    if (allActiveOffer && address) {
      // add filter for time if expiration time is less than current time
      const currentTime = Math.floor(Date.now() / 1000);
      const filteredListings = allActiveOffer[0]
        .filter((offer) => Number(offer.expirationTime) > currentTime)
        .filter(
          (offer) => offer.buyer.toLowerCase() === address?.toLowerCase(),
        );

      const sortedListings = filteredListings.sort((a, b) =>
        Number(b.pricePerToken - a.pricePerToken),
      );
      setAllActiveOfferSortedByPrice(sortedListings);
    }
  }, [allActiveOffer, address]);

  const { config: cancelOfferConfig } = usePrepareContractWrite({
    address: marketplaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "cancelOffer",
    args: [selectedOfferId],
    enabled:
      isConnected && marketplaceAddress !== "0x" && selectedOfferId !== 0n,
  });

  const { write: cancelOfferWrite, data: cancelOfferData } = useContractWrite({
    ...cancelOfferConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsCancelingOffer(false);
    },
  });

  const { isLoading: isWaitingForCancelOffer } = useWaitForTransaction({
    confirmations: 5,
    hash: cancelOfferData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Cancelled!");
      setIsCancelingOffer(false);
      onClose?.();
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsCancelingOffer(false);
    },
  });

  const cancelOffer = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    // if (errorMessage) {
    //   showError(errorMessage);
    //   return;
    // }

    if (!cancelOfferWrite) {
      showError("Failed! Try different network");
      return;
    }

    setIsCancelingOffer(true);
    try {
      cancelOfferWrite?.();
    } catch (error) {
      setIsCancelingOffer(false);
      showError("Failed to cancel offer");
    }
  };

  return {
    allActiveOfferSortedByPrice,
    formatUnixTimestamp,
    cancelOffer,
    isCancelingOffer,
    isWaitingForCancelOffer,
    setSelectedOfferId,
    selectedOfferId,
  };
};

export default useCancelOffer;
