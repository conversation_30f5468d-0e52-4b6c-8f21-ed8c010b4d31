import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useEffect, useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError } from "viem";
import {
  useAccount,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import { extractErrorType } from "@/utils/errorHandling";

const usePurchaseAllListingAmount = () => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [isPurchasingListing, setIsPurchasingListing] = useState(false);
  const [value, setValue] = useState(0n);
  const [listingId, setListingId] = useState(0n);
  const [amount, setAmount] = useState(0n);
  const [pricePerToken, setPricePerToken] = useState(0n);
  const [errorMessage, setErrorMessage] = useState("");
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setMarketplaceAddress("0x");
    }
  }, [chain]);

  useEffect(() => {
    if (pricePerToken && amount) {
      setValue(BigInt(amount) * BigInt(pricePerToken));
    }
  }, [amount, pricePerToken]);

  const { config: purchaseListingConfig, error: prepareError } =
    usePrepareContractWrite({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "purchaseListing",
      args: [listingId, amount],
      value,
      enabled:
        isConnected &&
        marketplaceAddress !== "0x" &&
        listingId !== 0n &&
        amount !== 0n &&
        value !== 0n,
      onError: (error: any) => {
        if (error.cause?.reason) {
          setErrorMessage(error.cause.reason);
        } else {
          setErrorMessage(error.shortMessage || error.message);
        }
      },
    });

  const { write: purchaseListingWrite, data: purchaseListingData } =
    useContractWrite({
      ...purchaseListingConfig,
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
        setIsPurchasingListing(false);
      },
    });

  const { isLoading: isWaitingForPurchaseListing } = useWaitForTransaction({
    confirmations: 5,
    hash: purchaseListingData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Purchased!");
      setIsPurchasingListing(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsPurchasingListing(false);
    },
  });

  const purchaseListing = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    // if (errorMessage) {
    //   showError(errorMessage);
    //   return;
    // }

    if (!purchaseListingWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      } else {
        showError(
          "Unable to process purchase. Please verify contract connection or switch to a supported network.",
        );
      }
      return;
    }

    setIsPurchasingListing(true);
    try {
      purchaseListingWrite?.();
    } catch (error) {
      setIsPurchasingListing(false);
      showError(extractErrorType(error) || "Failed to purchase listing");
    }
  };

  return {
    isWaitingForPurchaseListing,
    isPurchasingListing,
    purchaseListing,
    listingId,
    setAmount,
    setListingId,
    setPricePerToken,
  };
};

export default usePurchaseAllListingAmount;
