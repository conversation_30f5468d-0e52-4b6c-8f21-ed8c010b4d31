import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError } from "viem";
import {
  useAccount,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";

const useAcceptOffer = () => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [offerId, setOfferId] = useState(0n);
  const [isAcceptingOffer, setIsAcceptingOffer] = useState(false);
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setMarketplaceAddress("0x");
    }
  }, [chain]);

  const { config: acceptOfferConfig } = usePrepareContractWrite({
    address: marketplaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "acceptOffer",
    args: [offerId],
    enabled: isConnected && marketplaceAddress !== "0x" && offerId !== 0n,
  });

  const { write: acceptOfferWrite, data: acceptOfferData } = useContractWrite({
    ...acceptOfferConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsAcceptingOffer(false);
    },
  });

  const { isLoading: isWaitingForAcceptOffer } = useWaitForTransaction({
    confirmations: 5,
    hash: acceptOfferData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Accepted!");
      setIsAcceptingOffer(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsAcceptingOffer(false);
    },
  });

  const acceptOffer = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    // if (errorMessage) {
    //   showError(errorMessage);
    //   return;
    // }

    if (!acceptOfferWrite) {
      showError("Failed! Try different network");
      return;
    }

    setIsAcceptingOffer(true);
    try {
      acceptOfferWrite?.();
    } catch (error) {
      setIsAcceptingOffer(false);
      showError("Failed to accept offer");
    }
  };

  return {
    offerId,
    setOfferId,
    isAcceptingOffer,
    isWaitingForAcceptOffer,
    acceptOffer,
  };
};

export default useAcceptOffer;
