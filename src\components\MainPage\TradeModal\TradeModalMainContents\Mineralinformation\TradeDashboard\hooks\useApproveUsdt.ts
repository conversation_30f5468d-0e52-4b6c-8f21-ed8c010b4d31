import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError, parseUnits } from "viem";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import { extractErrorType } from "@/utils/errorHandling";
import { usdtABI } from "@/constants/abis/UsdtABI";

type ApproveUsdtProps = {
  usdtValue?: bigint;
  purchaseListing?: () => void;
  handleDecimals?: boolean;
};

const useApproveUsdt = ({
  purchaseListing,
  usdtValue,
  handleDecimals = false,
}: ApproveUsdtProps = {}) => {
  const [usdtAddress, setUsdtAddress] = useState<`0x${string}`>("0x");
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [usdtAllowance, setUsdtAllowance] = useState(0n);
  const [isApprovingUsdt, setIsApprovingUsdt] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [tokenDecimals, setTokenDecimals] = useState<number>(18);
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setUsdtAddress(networkConfigs[chain.id].usdtAddress);
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setUsdtAddress("0x");
      setMarketplaceAddress("0x");
    }
  }, [chain]);

  useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "decimals",
    enabled: isConnected && usdtAddress !== "0x",
    onSuccess: (data) => {
      setTokenDecimals(Number(data));
    },
    onError: () => {
      setTokenDecimals(18);
    },
  });

  useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "allowance",
    args: [address ?? "0x", marketplaceAddress],
    enabled:
      isConnected &&
      usdtAddress !== "0x" &&
      marketplaceAddress !== "0x" &&
      address !== undefined,
    watch: true,
    onSuccess: (data) => {
      setUsdtAllowance(data);
    },
    onError: () => {
      setUsdtAllowance(0n);
    },
  });

  const { config: approveUsdtConfig, error: prepareError } =
    usePrepareContractWrite({
      address: usdtAddress,
      abi: usdtABI,
      functionName: "approve",
      args: [
        marketplaceAddress,
        handleDecimals && usdtValue !== undefined
          ? parseUnits(usdtValue.toString(), tokenDecimals)
          : usdtValue ?? 0n,
      ],
      enabled:
        isConnected &&
        usdtAddress !== "0x" &&
        marketplaceAddress !== "0x" &&
        usdtValue !== undefined &&
        (usdtValue ?? 0n) > 0n,
      onError: (error: any) => {
        if (error.cause?.reason) {
          setErrorMessage(error.cause.reason);
        } else {
          setErrorMessage(error.shortMessage || error.message);
        }
      },
    });

  const { write: approveUsdtWrite, data: approveUsdtData } = useContractWrite({
    ...approveUsdtConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsApprovingUsdt(false);
    },
  });

  const { isLoading: isWaitingForApproveUsdt } = useWaitForTransaction({
    confirmations: 10,
    hash: approveUsdtData?.hash,
    onSuccess: () => {
      if (purchaseListing) {
        purchaseListing();
      }
      showSuccess("Successfully approved USDT");
      setIsApprovingUsdt(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsApprovingUsdt(false);
    },
  });

  const handleApproveUsdt = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (errorMessage) {
      showError(errorMessage);
      return;
    }

    if (!approveUsdtWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      } else {
        showError(
          "Unable to process approval. Please verify contract connection or switch to a supported network.",
        );
      }
      return;
    }

    setIsApprovingUsdt(true);
    try {
      approveUsdtWrite?.();
    } catch (error) {
      setIsApprovingUsdt(false);
      showError(extractErrorType(error) || "Failed to approve USDT");
    }
  };

  return {
    usdtAllowance,
    handleApproveUsdt,
    isApprovingUsdt,
    isWaitingForApproveUsdt,
    tokenDecimals,
  };
};

export default useApproveUsdt;
