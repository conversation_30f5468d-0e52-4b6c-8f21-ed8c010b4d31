import { poppins, saira, mavenPro } from "@/styles/fonts";
import "./globals.scss";
import type { Metadata } from "next";
import { WagmiProvider } from "./WagmiProvider";
import GoogleTags from "@/components/Analytics/GoogleTags";

export const metadata: Metadata = {
  manifest: "/mainfest.json",
  title: "AsteroidX",
  description:
    "AsteroidX is an incubation center that offers physical asset digitization, pegging and equity trading services for early-stage physical mining projects, leveraging web3 technology",
};

export const viewport = {
  themeColor: "#FFFFFF",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${saira.className} ${saira.variable} ${mavenPro.variable}`}
    >
      <body>
        <WagmiProvider>{children}</WagmiProvider>
      </body>
      <GoogleTags />
    </html>
  );
}
