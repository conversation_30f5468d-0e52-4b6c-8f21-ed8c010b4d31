import Linechart from "@/components/UI/Charts/Linechart/Linechart";
import { MinePurchased } from "../hooks/usePurchaseHistory";
import styles from "./CommoditiesStat.module.scss";
import {
  commoditiesDetails,
  getMetalsDetails,
} from "@/constants/commoditiesDetails";
import { useEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { metalsDataStore } from "@/stores/metalsData";
// import HskPriceChart from "@/components/UI/Charts/HskPriceChart/HskPriceChart";
// import { hashkey, hashkeyTestnet } from "@/constants/customChains";

const CommoditiesStat = ({
  mineDetails,
}: {
  mineDetails: MinePurchased["mineDetails"];
}) => {
  const [data, setData] = useState<(string | number)[][]>([]);
  const metalsDataSnapshot = useSnapshot(metalsDataStore);

  useEffect(() => {
    if (metalsDataSnapshot.metalsData !== null) {
      const metalsData = getMetalsDetails(
        JSON.parse(JSON.stringify(metalsDataSnapshot.metalsData)),
      );
      const result = metalsData.filter(
        (commodity) => commodity.name === mineDetails.chartDataType,
      )[0].historicalData;
      setData(result);
    }
  }, [metalsDataSnapshot.metalsData]);

  return (
    <div className={styles.container}>
      {/* <HskPriceChart /> */}
      {/* {mineDetails.supportedNetwork.includes(hashkeyTestnet.id) ||
      mineDetails.supportedNetwork.includes(hashkey.id) ? (
        <HskPriceChart />
      ) : ( */}
      <Linechart
        data={data}
        title={`${mineDetails.chartDataType.toUpperCase()} Trending Price`}
      />
      {/* )} */}
    </div>
  );
};

export default CommoditiesStat;
