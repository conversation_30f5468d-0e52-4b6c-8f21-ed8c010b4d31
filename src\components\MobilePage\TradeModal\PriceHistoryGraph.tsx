import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
} from "recharts";
import useLineChartPriceHistory from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useLineChartPriceHistory";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { ContentType } from "recharts/types/component/Tooltip";

const PriceHistoryGraph = () => {
  const { priceHistory, isLoadingHistory } = useLineChartPriceHistory();
  const containerRef = useRef<HTMLDivElement>(null);

  if (isLoadingHistory) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          width: "100%",
          flexDirection: "column",
          textAlign: "center",
        }}
      >
        <div
          style={{
            color: "#00C4D0",
            fontSize: "1.25rem",
            marginBottom: "0.5rem",
          }}
        >
          Loading...
        </div>
        <div style={{ color: "#00656B", fontSize: "0.875rem" }}>
          Fetching price history data
        </div>
      </div>
    );
  }

  // Check if price history data is empty
  if (!isLoadingHistory && priceHistory.length === 0) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "250px",
          width: "100%",
          flexDirection: "column",
          textAlign: "center",
          border: "1px solid #00656B",
          borderRadius: "8px",
          backgroundColor: "rgba(0, 12, 23, 0.5)",
        }}
      >
        <div
          style={{
            color: "#00C4D0",
            fontSize: "1.25rem",
            marginBottom: "0.5rem",
          }}
        >
          No Price History Available
        </div>
        <div style={{ color: "#00656B", fontSize: "0.875rem" }}>
          Historical price data will appear here once available
        </div>
      </div>
    );
  }

  const CustomTooltip: ContentType<ValueType, NameType> = ({
    active,
    payload,
    label,
  }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "rgba(0, 12, 23, 0.85)",
            padding: "12px",
            border: "1px solid #00656B",
            borderRadius: "6px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <div
            style={{ marginBottom: "4px", color: "#00C4D0", fontWeight: 500 }}
          >
            Date: {label}
          </div>
          <div
            style={{ color: "#ffffff", fontSize: "1.1rem", fontWeight: 600 }}
          >
            Price: {payload[0].value}
          </div>
        </div>
      );
    }

    return null;
  };

  // 计算图表宽度，确保有足够空间显示所有数据点
  const chartWidth = Math.max(800, priceHistory.length * 60); // 每个数据点至少60px宽度

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "100%",
        minHeight: "250px",
      }}
    >
      <div
        ref={containerRef}
        style={{
          width: "100%",
          height: "100%",
          minHeight: "250px",
          overflowX: "auto",
          overflowY: "hidden",
          // 添加平滑滚动
          scrollBehavior: "smooth",
          // 移动端触摸滚动优化
          WebkitOverflowScrolling: "touch",
          // 隐藏滚动条但保持功能
          scrollbarWidth: "none",
          msOverflowStyle: "none",
        }}
        className="price-chart-container"
      >
        <div style={{ width: `${chartWidth}px`, height: "100%" }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={priceHistory}
              margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#00656B"
                vertical={false}
                horizontal={false}
              />
              <XAxis
                dataKey="date"
                stroke="#00656B"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={60}
                interval={0}
              />
              <YAxis stroke="#00656B" fontSize={12} width={60} />
              <Tooltip content={<CustomTooltip />} isAnimationActive={false} />
              <Line
                type="monotone"
                dataKey="price"
                fill="black"
                stroke="#00C4D0"
                strokeWidth={1.5}
                // dot={{ fill: "#00C4D0", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 3, stroke: "#00C4D0", strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default PriceHistoryGraph;
