import React from "react";
// import {
//   Bar<PERSON><PERSON>,
//   Bar,
//   Rectangle,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   Legend,
//   ResponsiveContainer,
// } from "recharts";
import usePriceHistory from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/usePriceHistory";
import styles from "./CurrentPrice.module.scss";

const PriceHistoryGraph = () => {
  const { priceHistory, analyzePriceHistory, loading, hasFetched } = usePriceHistory();
  const { lowest, highest, current } = analyzePriceHistory(priceHistory);


  const calculateProgressWidth = () => {
    if (lowest === highest) return 50; // 如果最高最低相同，居中
    return ((current - lowest) / (highest - lowest)) * 100;
  };

  if (loading || !hasFetched) {
    return (
      <div className="flex items-center justify-center h-full w-full flex-col text-center">
        <div className="text-[#00C4D0] text-xl mb-2">Loading...</div>
        <div className="text-[#00656B] text-sm">
          Fetching price history data
        </div>
      </div>
    );
  }

  if (priceHistory.length === 0) {
    return (
      <div className="flex items-center justify-center h-full w-full flex-col text-center">
        <div className="text-[#00C4D0] text-xl mb-2">
          No Price History Available
        </div>
        <div className="text-[#00656B] text-sm">
          Historical price data will appear here once available
        </div>
      </div>
    );
  }

  return (
    // <ResponsiveContainer width="100%" height="100%">
    //   <BarChart data={priceHistory} barSize={15}>
    //     <CartesianGrid
    //       strokeDasharray="3 3"
    //       stroke="#00656B"
    //       vertical={false}
    //     />
    //     <XAxis dataKey="date" stroke="#00656B" />
    //     <YAxis stroke="#00656B" />
    //     {/* <Tooltip /> */}
    //     <Bar
    //       dataKey="price"
    //       fill="black"
    //       stroke="#00C4D0"
    //       // label={{ fill: "#00C4D0", fontSize: "10" }}
    //     />
    //   </BarChart>
    // </ResponsiveContainer>
    <div className={styles.barContainer}>
      <h2>Current Price Trend</h2>

      <div className={styles.priceProgress}>
        <div
          className={styles.progressBar}
          style={{ width: `${calculateProgressWidth()}%` }}
        />
        <div
          className={styles.priceMarker}
          style={{ left: `${calculateProgressWidth()}%` }}
        />
      </div>

      <div className={styles.details}>
        <span className={styles.lowest}>Lowest: {lowest.toFixed(4)}</span>
        <span className={styles.current}>Current: {current.toFixed(4)}</span>
        <span className={styles.highest}>Highest: {highest.toFixed(4)}</span>
      </div>
    </div>
  );
};

export default PriceHistoryGraph;
