import { buyerInformation } from "@/sampleAssets/constants/buyerInformation";
import InformationCard from "./InformationCard/InformationCard";
import styles from "./TradeModalMainContents.module.scss";
import VerticalLine from "./VerticalLine/VerticalLine";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import Mineralinformation from "./Mineralinformation/Mineralinformation";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useState } from "react";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import PurchasedMine from "./PurchasedMine/PurchasedMine";

import {
  useAccount,
  useContractRead,
  useContractReads,
  useNetwork,
} from "wagmi";
import { launchPadAbi } from "@/constants/abis/launchPadAbi";

type ContractList = {
  address: `0x${string}`;
  abi: typeof asteroidAddressABI;
  functionName: string;
  args: BigInt[];
};

const PlusModalMainContents = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [contractList, setContractList] = useState<ContractList[]>([]);

  const { chain } = useNetwork();
  const { isConnected } = useAccount();

  const { data: nftSold } = useContractRead({
    address: launchPadAddress,
    abi: launchPadAbi,
    functionName: "getNftSold",
    args: [asteroidAddress],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      launchPadAddress !== "0x" &&
      asteroidAddress !== "0x",
  });

  const { data: seller } = useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "owner",
    watch: true,
    enabled: !chain?.unsupported && isConnected && asteroidAddress !== "0x",
  });

  const { data: buyers } = useContractReads({
    contracts: contractList,
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      asteroidAddress !== "0x" &&
      contractList.length > 0,
  });

  const getSelectedMineAddress = (chainId: number) => {
    return networkConfigs[chainId].asteroidAddress;
  };

  useEffect(() => {
    if (chain?.unsupported) {
      setLaunchPadAddress("0x");
      setAsteroidAddress("0x");
    } else {
      if (chain) {
        setLaunchPadAddress(networkConfigs[chain.id].launchPadAddress);
        setAsteroidAddress(getSelectedMineAddress(chain.id) ?? "0x");
      }
    }
  }, [chain]);

  useEffect(() => {
    if (nftSold) {
      const tempContractList: ContractList[] = [];
      [...Array(Number(nftSold))].map((_, index) => {
        tempContractList.push({
          address: asteroidAddress,
          abi: asteroidAddressABI,
          functionName: "creators",
          args: [BigInt(index)],
        });
      });
      setContractList(tempContractList);
    }
  }, [nftSold]);
  return (
    <div className={styles.container}>
      <div className={styles.buyerInfo}>
        {/* Details of the mines */}
        <div className={styles.scrollArea}>
          <PurchasedMine />
        </div>
      </div>
      <div className={styles.mainContent}>
        <Mineralinformation />
      </div>
    </div>
  );
};

export default PlusModalMainContents;
