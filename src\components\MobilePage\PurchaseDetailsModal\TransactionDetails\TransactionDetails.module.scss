@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.deepDetailsWrapper {
  padding: $padding-md;
  background: rgba($color-black-transparent-light, 0.3);
  border-radius: 12px;
  margin: $margin-md 0;
  border: 1px solid rgba($color-primary, 0.2);

  .deepDetailsContainer {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;

    .deepDetails {
      @include row-between;
      padding: $padding-sm $padding-md;
      background: rgba($color-black-transparent-medium, 0.5);
      border-radius: 8px;
      border: 1px solid rgba($color-primary, 0.1);
      transition: all 0.3s ease;
      min-height: 60px;
      align-items: center;

      &:hover {
        background: rgba($color-primary, 0.05);
        border-color: rgba($color-primary, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba($color-primary, 0.1);
      }

      h1 {
        font-size: $font-size-xs;
        text-transform: uppercase;
        color: rgba($color-primary-contrast, 0.8);
        font-weight: $font-weight-medium;
        letter-spacing: 0.5px;
        line-height: 1.2;
        max-width: 60%;
        margin: 0;
      }

      h2 {
        text-align: right;
        margin: 0;
        font-weight: $font-weight-semibold;
        line-height: 1.3;
      }

      .totalShare {
        font-size: $font-size-lg;
        color: $color-primary;
        font-weight: $font-weight-bold;
      }

      .shareSold {
        color: $color-warning;
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
      }

      .shareToPurchase {
        font-size: $font-size-lg;
        color: $color-warning;
        font-weight: $font-weight-bold;

        span {
          font-size: $font-size-sm;
          text-transform: uppercase;
          color: rgba($color-warning, 0.8);
          font-weight: $font-weight-medium;
        }
      }

      // 进度条样式优化
      progress {
        width: 100%;
        height: 8px;
        border-radius: 4px;
        border: none;
        background: rgba($color-primary, 0.2);
        margin: $margin-sm 0;

        &::-webkit-progress-bar {
          background: rgba($color-primary, 0.2);
          border-radius: 4px;
        }

        &::-webkit-progress-value {
          background: linear-gradient(90deg, $color-warning, $color-primary);
          border-radius: 4px;
        }

        &::-moz-progress-bar {
          background: linear-gradient(90deg, $color-warning, $color-primary);
          border-radius: 4px;
        }
      }
    }
  }
}

// Add these styles to your existing CSS module file

.investorAttractiveSection {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 140, 0, 0.05));
  border-radius: 12px;
  padding: $padding-lg;
  box-shadow:
    0 8px 24px rgba(255, 215, 0, 0.15),
    inset 0 1px 0 rgba(255, 215, 0, 0.2);
  border: 2px solid rgba(255, 215, 0, 0.4);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  margin-top: $margin-lg;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: shimmer 3s infinite;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow:
      0 12px 32px rgba(255, 215, 0, 0.25),
      inset 0 1px 0 rgba(255, 215, 0, 0.3);
    border-color: rgba(255, 215, 0, 0.7);
  }
}

.gradientText {
  background: linear-gradient(
    45deg,
    #ffd700,
    #f5a623,
    #ff8c00,
    #f5a623,
    #ffd700,
    #ffed4e
  );
  background-size: 300% auto;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientMove 4s ease-in-out infinite;
  font-weight: $font-weight-bold;
  letter-spacing: 1px;
  font-size: $font-size-sm;
  text-transform: uppercase;
  position: relative;
  z-index: 2;
}

.returnValue {
  color: #ffd700;
  font-weight: $font-weight-bold;
  font-size: $font-size-xl;
  text-shadow:
    0 0 10px rgba(255, 215, 0, 0.6),
    0 0 20px rgba(255, 215, 0, 0.3);
  transition: all 0.4s ease;
  position: relative;
  z-index: 2;

  &:hover {
    text-shadow:
      0 0 15px rgba(255, 215, 0, 0.9),
      0 0 30px rgba(255, 215, 0, 0.5);
    transform: scale(1.05);
  }
}

// 动画定义
@keyframes gradientMove {
  0%, 100% {
    background-position: 0% center;
  }
  50% {
    background-position: 100% center;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// 响应式优化
@media (max-width: 480px) {
  .deepDetailsWrapper {
    padding: $padding-sm;
    margin: $margin-sm 0;
  }

  .deepDetailsContainer {
    gap: $spacing-sm;

    .deepDetails {
      padding: $padding-xs $padding-sm;
      min-height: 50px;

      h1 {
        font-size: $font-size-2xs;
        max-width: 55%;
      }

      .totalShare,
      .shareSold,
      .shareToPurchase {
        font-size: $font-size-md;
      }
    }
  }

  .investorAttractiveSection {
    padding: $padding-md;
    margin-top: $margin-md;
  }

  .returnValue {
    font-size: $font-size-lg;
  }
}
