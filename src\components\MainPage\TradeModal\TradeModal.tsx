import { fadeIn } from "@/animations/animations";
import styles from "./TradeModal.module.scss";
import { motion } from "framer-motion";
import PlusModalMainContents from "./TradeModalMainContents/TradeModalMainContents";
import TradeModalHeader from "./TradeModalHeader/TradeModalHeader";
import { useSnapshot } from "valtio";
import { tradeModalStore } from "@/stores/tradeModal";

const TradeModal = () => {
  const tradeModalSnapshot = useSnapshot(tradeModalStore);

  return (
    <motion.div
      className={styles.container}
      variants={fadeIn(1)}
      initial="hidden"
      animate="visible"
      exit="hidden"
      transition={{ duration: 1 }}
    >
      <motion.div className={styles.modalFrame} variants={fadeIn(0.5)}>
        <div className={styles.sellMineOnline}>
          <h1>sell mine online</h1>
          <h1>2023</h1>
        </div>
        <div className={styles.contentWrapper}>
          <TradeModalHeader />
          <PlusModalMainContents />
        </div>
      </motion.div>
    </motion.div>
  );
};

export default TradeModal;
