import { fadeIn, topDown } from "@/animations/animations";
import styles from "./TopNavigation.module.scss";
import { motion } from "framer-motion";
import MarketDataCarousel from "./MarketDataCarousel/MarketDataCarousel";
import NavbarContent, {
  navbarButtonDetails,
} from "./NavbarContent/NavbarContent";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";

const TopNavigation = ({ delay = 2 }) => {
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const isShownMarketData =
    navbarButtonSnapshot.selectedButton === navbarButtonDetails[0].title;
  const isInitialAnimationDone = navbarButtonSnapshot.isInitialAnimationDone;
  return (
    <div className={styles.container}>
      <motion.div
        className={styles.trapezoid}
        variants={topDown(2, delay, -110)}
        initial="hidden"
        animate="visible"
      >
        <NavbarContent />
      </motion.div>
      {isShownMarketData ? (
        <motion.div
          className={styles.marketDataContainer}
          variants={fadeIn(2, isInitialAnimationDone ? 0 : delay + 1.5)}
          initial="hidden"
          animate="visible"
        >
          <MarketDataCarousel />
        </motion.div>
      ) : null}
    </div>
  );
};

export default TopNavigation;
