import { minesDetails } from "@/constants/mineDetails";
import styles from "./MineCard.module.scss";
import { motion } from "framer-motion";
import {
  buttonEffect,
  expandCard,
  fadeIn,
  rotation,
  scaleUp,
} from "@/animations/animations";
import flashingDot from "@/assets/icons/minesNavigation/ripple.svg";
import arrowDownIcon from "@/assets/icons/minesNavigation/arrowDown.png";
import { useEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";
import ArrowDownIcon from "../../TopNavigation/NavbarContent/components/ArrowDownIcon";
import MineHeader from "./MineHeader/MineHeader";
import MineBody from "./MineBody/MineBody";
import plusButton from "@/assets/images/plusButton.png";
import clickForDetailsButton from "@/assets/images/clickForDetailsButton.png";
import moreDetailsButton from "@/assets/images/moreDetailsButton.png";
import { mineCardStore } from "@/stores/mineCard";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import { plusModalStore } from "@/stores/plusModal";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import { useNetwork, useSwitchNetwork } from "wagmi";
import { showError } from "@/lib/notification";
import { set } from "zod";
import peckShieldIcon from "@/assets/icons/peckShieldIcon.png";

type Unpacked<T> = T extends (infer Item)[] ? Item : T;

export interface MineCardProps {
  isLaunchSection?: boolean;
  delay?: number;
  information: Unpacked<typeof minesDetails>;
}

const MineCard = ({ delay, information, isLaunchSection }: MineCardProps) => {
  const TARGET_HEIGHT = 780; //"fit-content";
  const [isCheckDetailsButtonClicked, setIsCheckDetailsButtonClicked] =
    useState(false);
  const [isInitialAnimationDone, setIsInitialAnimationDone] = useState(false);

  const mineCardSnapshot = useSnapshot(mineCardStore);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const plusModalSnapshot = useSnapshot(plusModalStore);
  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const { chain } = useNetwork();
  const { chains } = useSwitchNetwork();

  useEffect(() => {
    const timeout = setTimeout(
      () => {
        // if (
        //   mineCardSnapshot.selectedMine === information.name &&
        //   !isLaunchSection
        // ) {
        //   setIsCheckDetailsButtonClicked(true);
        // }
        setIsInitialAnimationDone(true);
      },
      delay && (delay + 3.5) * 1000,
    );

    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    if (isCheckDetailsButtonClicked) {
      if (connectWalletModalSnapshot.isOpenConnectWalletModal) {
        setIsCheckDetailsButtonClicked(false);
      }

      mineCardSnapshot.setSelectedMine(information.name);
      mineCardSnapshot.setIsShowOverlay(true);
    } else {
      mineCardSnapshot.setIsShowOverlay(false);
    }
  }, [isCheckDetailsButtonClicked]);

  useEffect(() => {
    if (mineCardSnapshot.selectedMine !== information.name) {
      setIsCheckDetailsButtonClicked(false);
    }
  }, [mineCardSnapshot.selectedMine]);

  useEffect(() => {
    if (purchaseDetailsModal.isOpenPurchaseDetailsModal) {
      setIsCheckDetailsButtonClicked(false);
    }
  }, [purchaseDetailsModal.isOpenPurchaseDetailsModal]);

  useEffect(() => {
    if (connectWalletModalSnapshot.isOpenConnectWalletModal) {
      if (isInitialAnimationDone) {
        setIsCheckDetailsButtonClicked(false);
      }
    }
    // else {
    //   if (mineCardSnapshot.selectedMine === information.name) {
    //     if (isInitialAnimationDone) {
    //       setIsCheckDetailsButtonClicked(true);
    //     }
    //   }
    // }
  }, [connectWalletModalSnapshot.isOpenConnectWalletModal]);

  const [hasErrorShown, setHasErrorShown] = useState(false);
  const handleMouseEnter = () => {
    mineCardSnapshot.setSelectedMine(information.name);
    // if (chain && !information.supportedNetwork.includes(chain?.id)) {
    //   // filter out the networks that are not supported
    //   if (!hasErrorShown) {
    //     const supportedChain = chains.filter((x) =>
    //       information.supportedNetwork.includes(x.id),
    //     )[0].name;
    //     showError(`${information.name} is supported on ${supportedChain}`);
    //     setHasErrorShown(true);
    //   }
    // }
  };
  return (
    <>
      <motion.img
        className={styles.rippleContainer}
        variants={scaleUp(0.8, delay)}
        src={flashingDot.src}
        width={72}
        height={72}
        alt="flashing dot"
        style={{
          top: information.position.rippleTop,
          left: information.position.rippleLeft,
        }}
      />
      <motion.div
        onMouseEnter={handleMouseEnter}
        variants={
          isCheckDetailsButtonClicked
            ? expandCard(TARGET_HEIGHT, "20%", "42%", 0.3, 0.1)
            : scaleUp(0.8, delay && delay + 1.8)
        }
        className={styles.container}
        initial="hidden"
        animate="visible"
        transition={{ duration: 0.3 }}
        style={{
          top: information.position.top,
          left: information.position.left,
          zIndex: isCheckDetailsButtonClicked ? 1 : undefined,
        }}
      >
        {isCheckDetailsButtonClicked ? (
          <>
            <motion.button
              variants={fadeIn(0.1, 0.5)}
              className={styles.closeButton}
              onClick={() => {
                setIsCheckDetailsButtonClicked(!isCheckDetailsButtonClicked);
              }}
            >
              <img src={crossIcon.src} alt="cross icon" />
            </motion.button>
            <motion.img
              whileTap={buttonEffect.tap}
              whileHover={buttonEffect.hover}
              variants={fadeIn(0.5, 0.5)}
              src={moreDetailsButton.src}
              alt="plus button"
              className={styles.plusButton}
              onClick={() => {
                plusModalSnapshot.setIsOpenPlusModal(true);
              }}
            />
          </>
        ) : null}
        <motion.div
          className={styles.cardwrapper}
          whileTap={!isCheckDetailsButtonClicked ? buttonEffect.tap : undefined}
          whileHover={
            !isCheckDetailsButtonClicked
              ? {
                  x: -5,
                  transition: {
                    duration: 0.5,
                    repeat: Infinity,
                    repeatType: "reverse",
                  },
                }
              : {
                  x: 0,
                  transition: {
                    duration: 0,
                    repeat: Infinity,
                    repeatType: "reverse",
                  },
                }
          }
          onClick={() => {
            !isCheckDetailsButtonClicked
              ? setIsCheckDetailsButtonClicked(!isCheckDetailsButtonClicked)
              : null;
          }}
        >
          <div
            className={styles.cardHeader}
            style={{
              display: isCheckDetailsButtonClicked ? "flex" : undefined,
              flexDirection: isCheckDetailsButtonClicked ? "column" : undefined,
              justifyContent: isCheckDetailsButtonClicked
                ? "flex-start"
                : undefined,
              alignItems: isCheckDetailsButtonClicked
                ? "flex-start"
                : undefined,
            }}
          >
            {chain &&
            information.supportedNetwork.includes(chain?.id) ? null : (
              <div className={styles.overlay} />
            )}
            <MineHeader information={information} />
            {isCheckDetailsButtonClicked ? (
              <MineBody information={information} />
            ) : null}
          </div>
          <motion.div
            className={styles.checkDetailsButton}
            onClick={() => {
              setIsCheckDetailsButtonClicked(!isCheckDetailsButtonClicked);
            }}
            style={{
              cursor: isCheckDetailsButtonClicked ? "pointer" : undefined,
            }}
          >
            <h1 className={styles.checkDetailsButtonText}>
              {isCheckDetailsButtonClicked ? (
                <h5 className={styles.auditedText}>
                  *Audited by
                  <img
                    src={peckShieldIcon.src}
                    alt="peckShieldIcon"
                    height={14}
                  />
                </h5>
              ) : (
                "Check The Details"
              )}
            </h1>
            {isCheckDetailsButtonClicked ? null : (
              <img
                src={arrowDownIcon.src}
                alt="arrow down icon"
                style={{
                  transform: isCheckDetailsButtonClicked
                    ? "rotate(180deg)"
                    : undefined,
                }}
              />
            )}
          </motion.div>
        </motion.div>
      </motion.div>
    </>
  );
};

export default MineCard;
