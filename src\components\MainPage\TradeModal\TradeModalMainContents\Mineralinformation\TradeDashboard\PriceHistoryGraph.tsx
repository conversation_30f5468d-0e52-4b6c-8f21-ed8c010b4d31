import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  Rectangle,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import usePriceHistory from "./hooks/usePriceHistory";

const PriceHistoryGraph = () => {
  const { priceHistory, loading, hasFetched } = usePriceHistory();

  if (loading || !hasFetched) {
    return (
      <div className="flex items-center justify-center h-full w-full flex-col text-center">
        <div className="text-[#00C4D0] text-xl mb-2">Loading...</div>
        <div className="text-[#00656B] text-sm">
          Fetching price history data
        </div>
      </div>
    );
  }

  // Check if price history data is empty
  if (priceHistory.length === 0) {
    return (
      <div className="flex items-center justify-center h-full w-full flex-col text-center">
        <div className="text-[#00C4D0] text-xl mb-2">
          No Price History Available
        </div>
        <div className="text-[#00656B] text-sm">
          Historical price data will appear here once available
        </div>
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={priceHistory} barSize={40}>
        <CartesianGrid
          strokeDasharray="3 3"
          stroke="#00656B"
          vertical={false}
        />
        <XAxis dataKey="date" stroke="#00656B" />
        <YAxis stroke="#00656B" />
        {/* <Tooltip /> */}
        <Bar
          dataKey="price"
          fill="black"
          stroke="#00C4D0"
          label={{ fill: "#00C4D0", fontSize: "12" }}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default PriceHistoryGraph;
