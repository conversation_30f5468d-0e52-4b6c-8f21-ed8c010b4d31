import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";

// Function to convert Unix timestamp to human readable format
const formatUnixTimestamp = (timestamp: number): string => {
  if (!timestamp) return "";

  const date = new Date(timestamp * 1000);
  return date.toLocaleString("en-AU", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

const useActiveListing = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [allActiveListingSortedByPrice, setAllActiveListingSortedByPrice] =
    useState<typeof allActiveListingsForToken>([]);
  const [
    allActiveListingSortedByPriceCombined,
    setAllActiveListingSortedByPriceCombined,
  ] = useState<typeof allActiveListingsForToken>([]);
  const [
    allActiveListingSortedByPriceFiltered,
    setAllActiveListingSortedByPriceFiltered,
  ] = useState<typeof allActiveListingsForToken>([]);
  const [filterValue, setFilterValue] = useState(0n);
  // -------------------------------------------------
  // below are for the selected listing used for the currentPrice.tsx
  const [isPurchasable, setIsPurchasable] = useState(false);
  const [expirationTime, setExpirationTime] = useState("");
  const [maxPurchasableAmount, setMaxPurchasableAmount] = useState(0);
  const [listingId, setListingId] = useState(0n);
  const [pricePerToken, setPricePerToken] = useState(0n);
  // -------------------------------------------------
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const { data: allActiveListingsForToken } = useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getAllActiveListingsForToken",
    args: [BigInt(asteroidMineNumber)],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
  });

  useEffect(() => {
    if (allActiveListingsForToken && address) {
      // add filter for time if expiration time is less than current time
      const currentTime = Math.floor(Date.now() / 1000);
      const filteredListings = allActiveListingsForToken
        .filter((listing) => Number(listing.expirationTime) > currentTime)
        .filter(
          (listing) => listing.seller.toLowerCase() !== address?.toLowerCase(),
        );
      const sortedListings = filteredListings
        .sort((a, b) => Number(a.timestamp - b.timestamp))
        .sort((a, b) => Number(a.pricePerToken - b.pricePerToken));

      setIsPurchasable(sortedListings.length > 0);
      setAllActiveListingSortedByPrice(sortedListings);
      setExpirationTime(
        formatUnixTimestamp(Number(sortedListings[0]?.expirationTime)),
      );
      setMaxPurchasableAmount(Number(sortedListings[0]?.amount));
      setListingId(sortedListings[0]?.listingId);
      setPricePerToken(sortedListings[0]?.pricePerToken);
    }
  }, [allActiveListingsForToken, address]);

  useEffect(() => {
    if (allActiveListingsForToken && address) {
      const currentTime = Math.floor(Date.now() / 1000);
      const filteredListings = allActiveListingsForToken.filter(
        (listing) => Number(listing.expirationTime) > currentTime,
      );
      const sortedListings = filteredListings
        .sort((a, b) => Number(a.timestamp - b.timestamp))
        .sort((a, b) => Number(a.pricePerToken - b.pricePerToken));
      // Group listings by price per token
      const groupedByPrice = filteredListings.reduce(
        (acc, listing) => {
          const priceKey = listing.pricePerToken.toString();
          if (!acc[priceKey]) {
            acc[priceKey] = {
              ...listing,
              amount: 0n,
              totalPrice: 0n,
              originalListings: [],
            };
          }
          acc[priceKey].amount += listing.amount;
          acc[priceKey].totalPrice =
            acc[priceKey].pricePerToken * acc[priceKey].amount;
          acc[priceKey].originalListings.push(listing);
          return acc;
        },
        {} as Record<string, any>,
      );

      // Convert grouped listings back to array
      const combinedListings = Object.values(groupedByPrice);

      // Sort combined listings by price
      const sortedCombinedListings = combinedListings.sort((a, b) =>
        Number(a.pricePerToken - b.pricePerToken),
      );

      setAllActiveListingSortedByPriceCombined(sortedCombinedListings);
      setAllActiveListingSortedByPriceFiltered(
        sortedListings.filter(
          (listing) => listing.pricePerToken === filterValue,
        ),
      );
    }
  }, [allActiveListingsForToken, address, filterValue]);

  return {
    allActiveListingSortedByPrice,
    allActiveListingSortedByPriceCombined,
    allActiveListingSortedByPriceFiltered,
    expirationTime,
    isPurchasable,
    maxPurchasableAmount,
    listingId,
    pricePerToken,
    formatUnixTimestamp,
    setFilterValue,
  };
};

export default useActiveListing;
