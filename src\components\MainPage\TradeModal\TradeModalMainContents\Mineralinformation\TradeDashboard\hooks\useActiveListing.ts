import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";

type Listing = {
  listingId: bigint;
  seller: `0x${string}`;
  tokenId: bigint;
  amount: bigint;
  pricePerToken: bigint;
  expirationTime: bigint;
  active: boolean;
  timestamp: bigint;
  paymentToken: `0x${string}`;
};

// Function to convert Unix timestamp to human readable format
const formatUnixTimestamp = (timestamp: number): string => {
  if (!timestamp) return "";

  const date = new Date(timestamp * 1000);
  return date.toLocaleString("en-AU", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

const useActiveListing = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [allActiveListingSortedByPrice, setAllActiveListingSortedByPrice] =
    useState<Listing[]>([]);
  const [
    allActiveListingSortedByPriceCombined,
    setAllActiveListingSortedByPriceCombined,
  ] = useState<Listing[]>([]);
  const [
    allActiveListingSortedByPriceFiltered,
    setAllActiveListingSortedByPriceFiltered,
  ] = useState<Listing[]>([]);
  const [filterValue, setFilterValue] = useState(0n);
  // -------------------------------------------------
  // below are for the selected listing used for the currentPrice.tsx
  const [isPurchasable, setIsPurchasable] = useState(false);
  const [expirationTime, setExpirationTime] = useState("");
  const [maxPurchasableAmount, setMaxPurchasableAmount] = useState(0);
  const [listingId, setListingId] = useState(0n);
  const [pricePerToken, setPricePerToken] = useState(0n);
  // -------------------------------------------------
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const { data: allActiveListingsForToken } = useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getAllActiveListingsForToken",
    args: [BigInt(asteroidMineNumber)],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
  }) as { data?: Listing[] }; // Cast the data to Listing[]

  useEffect(() => {
    if (allActiveListingsForToken && address) {
      // Contract already filters for active and non-expired listings.
      // Filter out listings by the current user.
      const filteredListings = allActiveListingsForToken.filter(
        (listing: Listing) =>
          listing.seller.toLowerCase() !== address?.toLowerCase(),
      );

      // Sort by timestamp (oldest first for tie-breaking if needed), then by price (lowest first).
      const sortedListings = [...filteredListings]
        .sort((a: Listing, b: Listing) => Number(a.timestamp - b.timestamp))
        .sort((a: Listing, b: Listing) =>
          Number(a.pricePerToken - b.pricePerToken),
        );

      setIsPurchasable(sortedListings.length > 0);
      setAllActiveListingSortedByPrice(sortedListings);

      if (sortedListings.length > 0) {
        setExpirationTime(
          formatUnixTimestamp(Number(sortedListings[0].expirationTime)),
        );
        setMaxPurchasableAmount(Number(sortedListings[0].amount));
        setListingId(sortedListings[0].listingId);
        setPricePerToken(sortedListings[0].pricePerToken);
      } else {
        // Reset if no purchasable listings
        setExpirationTime("");
        setMaxPurchasableAmount(0);
        setListingId(0n);
        setPricePerToken(0n);
      }
    } else if (!allActiveListingsForToken && address) {
      // Handle case where allActiveListingsForToken might be undefined or null after a fetch
      setIsPurchasable(false);
      setAllActiveListingSortedByPrice([]);
      setExpirationTime("");
      setMaxPurchasableAmount(0);
      setListingId(0n);
      setPricePerToken(0n);
    }
  }, [allActiveListingsForToken, address]);

  useEffect(() => {
    if (allActiveListingsForToken && address) {
      // allActiveListingsForToken is already filtered for active and non-expired by the contract.

      // For grouping by price, use all listings returned by the contract (not filtered by seller).
      const listingsForGrouping: Listing[] = allActiveListingsForToken;

      const groupedByPrice = listingsForGrouping.reduce(
        (acc: Record<string, any>, listing: Listing) => {
          const priceKey = listing.pricePerToken.toString();
          if (!acc[priceKey]) {
            acc[priceKey] = {
              ...listing,
              amount: 0n,
              totalPrice: 0n,
              originalListings: [],
            };
          }
          acc[priceKey].amount += listing.amount;
          acc[priceKey].totalPrice =
            acc[priceKey].pricePerToken * acc[priceKey].amount;
          acc[priceKey].originalListings.push(listing);
          return acc;
        },
        {} as Record<string, any>,
      );

      const combinedListings: Listing[] = Object.values(groupedByPrice).map(
        (item) => ({
          ...item,
        }),
      );

      const sortedCombinedListings = [...combinedListings].sort(
        (a: Listing, b: Listing) => Number(a.pricePerToken - b.pricePerToken),
      );

      setAllActiveListingSortedByPriceCombined(sortedCombinedListings);

      const sortedListingsForFilteringByPrice = allActiveListingsForToken
        .filter(
          (listing: Listing) =>
            listing.seller.toLowerCase() !== address?.toLowerCase(),
        )
        .sort((a: Listing, b: Listing) => Number(a.timestamp - b.timestamp))
        .sort((a: Listing, b: Listing) =>
          Number(a.pricePerToken - b.pricePerToken),
        );

      setAllActiveListingSortedByPriceFiltered(
        sortedListingsForFilteringByPrice.filter(
          (listing: Listing) => listing.pricePerToken === filterValue,
        ),
      );
    } else if (!allActiveListingsForToken && address) {
      setAllActiveListingSortedByPriceCombined([]);
      setAllActiveListingSortedByPriceFiltered([]);
    }
  }, [allActiveListingsForToken, address, filterValue]);

  return {
    allActiveListingSortedByPrice,
    allActiveListingSortedByPriceCombined,
    allActiveListingSortedByPriceFiltered,
    expirationTime,
    isPurchasable,
    maxPurchasableAmount,
    listingId,
    pricePerToken,
    formatUnixTimestamp,
    setFilterValue,
  };
};

export default useActiveListing;
