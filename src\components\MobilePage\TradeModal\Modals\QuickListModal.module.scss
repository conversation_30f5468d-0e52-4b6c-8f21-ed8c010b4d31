@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(10deg) rotateY(0deg);
$download-rotation-angle: rotateX(6deg) rotateY(0deg);

.wrapper {
  position: fixed;
  top: 52%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px; /* 可以根据实际需求调整最大宽度 */
  height: 85%;
  max-height: 600px; /* 可以根据实际需求调整最大高度 */
  z-index: 1000;
  background: $color-black-transparent;
  backdrop-filter: blur(10px);
  // mask-image: linear-gradient(to bottom, black 85%, transparent);
  padding: $padding-md;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    // background: linear-gradient(
    //   to bottom,
    //   $color-primary,
    //   $color-black-transparent
    // );
    box-shadow: 0 0 10rem 10rem $color-primary;

    clip-path: polygon(
      0 0,
      calc(100% - 32px) 0,
      100% 32px,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      1px calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 1px) calc(32px + 0.41px),
      calc(100% - 32px - 0.41px) 1px,
      1px 1px
    );
  }
  .header {
    font-family: "Garalama", sans-serif;
    font-weight: $font-weight-extralight;
    letter-spacing: 1px;
    width: 100%;
    color: #00e0ff;
  }
}

.contentWrapper {
  width: 100%;
  height: 100%;
  padding: $padding-md 0;
  @include col-center;
  gap: 1rem;

  .scrollArea {
    width: 100%;
    height: 100%;
    overflow: scroll;
    scroll-behavior: smooth;
    display: flex; /* 新增 */
    flex-direction: column; /* 新增 */
    gap: 1rem;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}
.headerWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  // margin-bottom: 1rem;

  .closeButton {
    margin-top: 0.5rem;
  }
}
.derlordInfo {
  h3 {
    color: #00e0ff;
    font-size: 24px;
  }

  span {
    display: block;
    color: #00e0ff;
  }

  p {
    color: #00e0ff;
    font-size: 14px;
  }
}

.itemCount,
.priceSection,
.durationSection {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;

  h3,
  h4 {
    color: $color-primary;
    font-size: 1rem;
  }

  h4 {
    opacity: 0.8;
  }
}

.inputContainer,
.priceInput,
.durationControls {
  @include row-between;
  gap: 1rem;
  width: 100%;
  // border: 1px solid $color-primary-contrast;

  input,
  select {
    border-radius: 0.5rem;
    flex: 1;
    background: rgba(0, 224, 255, 0.2);
    border: 1px solid $color-primary-contrast;
    color: #00e0ff;
    padding: 0.5rem;
    outline: none;
    font-size: 16px;
    
    &::placeholder {
      color: $color-primary;
      opacity: 0.5;
    }
  }

  .available,
  .eth {
    display: flex;
    align-items: center;
    color: $color-primary;
    padding: 1rem 0;
  }
}

.durationControls {
  select {
    font-size: $font-size-sm;
    max-width: 200px;
  }

  input[type="datetime-local"] {
    font-size: $font-size-sm;
    flex: 2;
  }
}

.summary {
  display: flex;
  flex-direction: column;
  // gap: 0.2rem;

  .summaryRow {
    display: flex;
    justify-content: space-between;
    color: $color-primary-contrast;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba($color-primary, 0.2);

    &:last-child {
      border-bottom: none;
    }
  }
}

.completeButton {
  border-radius: 0.5rem;
  width: 100%;
  padding: 1rem;
  margin-bottom: 2rem;
  background: rgba(0, 224, 255, 0.2);
  border: 1px solid $color-primary-contrast;
  color: $color-primary;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba($color-primary, 0.2);
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}
